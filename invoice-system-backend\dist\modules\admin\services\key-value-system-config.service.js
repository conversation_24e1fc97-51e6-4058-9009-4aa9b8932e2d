"use strict";
/**
 * Key-Value模式系统配置服务
 * 支持灵活的配置管理
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.KeyValueSystemConfigService = void 0;
class KeyValueSystemConfigService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    /**
     * 获取单个配置项
     */
    async getConfig(key) {
        const config = await this.prisma.systemConfig.findUnique({
            where: { key }
        });
        if (!config) {
            return null;
        }
        // 根据类型解析值
        switch (config.type) {
            case 'json':
                try {
                    return JSON.parse(config.value);
                }
                catch {
                    return {};
                }
            case 'number':
                return Number(config.value);
            case 'boolean':
                return config.value === 'true';
            default:
                return config.value;
        }
    }
    /**
     * 设置单个配置项
     */
    async setConfig(key, value, type = 'string', category = 'general', description) {
        let stringValue;
        // 根据类型转换值
        switch (type) {
            case 'json':
                stringValue = JSON.stringify(value);
                break;
            case 'number':
            case 'boolean':
                stringValue = String(value);
                break;
            default:
                stringValue = value;
        }
        await this.prisma.systemConfig.upsert({
            where: { key },
            update: {
                value: stringValue,
                type,
                category,
                description
            },
            create: {
                key,
                value: stringValue,
                type,
                category,
                description
            }
        });
    }
    /**
     * 获取分类下的所有配置
     */
    async getConfigsByCategory(category) {
        const configs = await this.prisma.systemConfig.findMany({
            where: { category }
        });
        const result = {};
        for (const config of configs) {
            result[config.key] = await this.parseConfigValue(config);
        }
        return result;
    }
    /**
     * 获取完整的系统配置
     */
    async getSystemConfig() {
        const [apiUrl, aid, appSecret, callbackUrl, regionMappings] = await Promise.all([
            this.getConfig('api_url'),
            this.getConfig('aid'),
            this.getConfig('app_secret'),
            this.getConfig('callback_url'),
            this.getConfig('region_mappings')
        ]);
        return {
            apiUrl: apiUrl || '',
            aid: aid || '',
            appSecret: appSecret || '',
            callbackUrl: callbackUrl || '',
            regionMappings: regionMappings || {}
        };
    }
    /**
     * 保存完整的系统配置
     */
    async saveSystemConfig(data) {
        await Promise.all([
            this.setConfig('api_url', data.apiUrl, 'string', 'api', 'API基础地址'),
            this.setConfig('aid', data.aid, 'string', 'api', '应用ID'),
            this.setConfig('app_secret', data.appSecret, 'string', 'api', '应用密钥'),
            this.setConfig('callback_url', data.callbackUrl, 'string', 'api', '回调地址'),
            this.setConfig('region_mappings', data.regionMappings, 'json', 'region', '地区编号映射')
        ]);
        return data;
    }
    /**
     * 获取地区编号映射
     */
    async getRegionMappings() {
        return await this.getConfig('region_mappings') || {};
    }
    /**
     * 根据地区名称获取编号
     */
    async getRegionCode(regionName) {
        const mappings = await this.getRegionMappings();
        return mappings[regionName] || null;
    }
    /**
     * 获取API配置
     */
    async getApiConfig() {
        const [apiUrl, aid, appSecret, callbackUrl] = await Promise.all([
            this.getConfig('api_url'),
            this.getConfig('aid'),
            this.getConfig('app_secret'),
            this.getConfig('callback_url')
        ]);
        if (!apiUrl || !aid || !appSecret) {
            return null;
        }
        return { apiUrl, aid, appSecret, callbackUrl: callbackUrl || '' };
    }
    /**
     * 获取所有配置项（用于管理界面）
     */
    async getAllConfigs() {
        const configs = await this.prisma.systemConfig.findMany({
            orderBy: [{ category: 'asc' }, { key: 'asc' }]
        });
        return configs.map(config => ({
            key: config.key,
            value: config.value,
            type: config.type,
            category: config.category,
            description: config.description || undefined
        }));
    }
    /**
     * 批量设置配置项
     */
    async setConfigs(configs) {
        await Promise.all(configs.map(config => this.setConfig(config.key, config.value, config.type, config.category, config.description)));
    }
    /**
     * 删除配置项
     */
    async deleteConfig(key) {
        await this.prisma.systemConfig.delete({
            where: { key }
        });
    }
    /**
     * 解析配置值
     */
    async parseConfigValue(config) {
        switch (config.type) {
            case 'json':
                try {
                    return JSON.parse(config.value);
                }
                catch {
                    return {};
                }
            case 'number':
                return Number(config.value);
            case 'boolean':
                return config.value === 'true';
            default:
                return config.value;
        }
    }
}
exports.KeyValueSystemConfigService = KeyValueSystemConfigService;
//# sourceMappingURL=key-value-system-config.service.js.map