"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaxCodeService = void 0;
class TaxCodeService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    // 获取税收编码列表
    async findAll(filters) {
        try {
            // 构建查询条件
            const where = {};
            if (filters.search) {
                where.OR = [
                    { categoryName: { contains: filters.search, mode: 'insensitive' } },
                    { categoryCode: { contains: filters.search } },
                    { keywords: { contains: filters.search, mode: 'insensitive' } }
                ];
            }
            if (filters.isActive !== undefined) {
                where.isActive = filters.isActive;
            }
            if (filters.isSummary !== undefined) {
                where.isSummary = filters.isSummary;
            }
            // 分页参数
            const page = filters.page || 1;
            const pageSize = filters.pageSize || 50;
            const skip = (page - 1) * pageSize;
            // 查询总数
            const total = await this.prisma.taxCode.count({ where });
            // 查询数据
            const items = await this.prisma.taxCode.findMany({
                where,
                skip,
                take: pageSize,
                orderBy: { categoryCode: 'asc' }
            });
            return {
                items,
                total,
                page,
                pageSize,
                totalPages: Math.ceil(total / pageSize)
            };
        }
        catch (error) {
            console.error('获取税收编码列表失败:', error);
            throw new Error('获取税收编码列表失败');
        }
    }
    // 根据ID获取税收编码
    async findById(id) {
        try {
            const taxCode = await this.prisma.taxCode.findUnique({
                where: { id }
            });
            return taxCode;
        }
        catch (error) {
            console.error('获取税收编码失败:', error);
            throw error;
        }
    }
    // 创建税收编码
    async create(data) {
        try {
            // 检查编码是否已存在
            const existing = await this.prisma.taxCode.findUnique({
                where: { categoryCode: data.categoryCode }
            });
            if (existing) {
                throw new Error('税收编码已存在');
            }
            // 创建新记录
            const taxCode = await this.prisma.taxCode.create({
                data
            });
            return taxCode;
        }
        catch (error) {
            console.error('创建税收编码失败:', error);
            throw error;
        }
    }
    // 更新税收编码
    async update(id, data) {
        try {
            // 检查记录是否存在
            const existing = await this.prisma.taxCode.findUnique({
                where: { id }
            });
            if (!existing) {
                throw new Error('税收编码不存在');
            }
            // 如果更新编码，检查新编码是否已被其他记录使用
            if (data.categoryCode && data.categoryCode !== existing.categoryCode) {
                const codeExists = await this.prisma.taxCode.findUnique({
                    where: { categoryCode: data.categoryCode }
                });
                if (codeExists) {
                    throw new Error('税收编码已存在');
                }
            }
            // 更新记录
            const taxCode = await this.prisma.taxCode.update({
                where: { id },
                data
            });
            return taxCode;
        }
        catch (error) {
            console.error('更新税收编码失败:', error);
            throw error;
        }
    }
    // 删除税收编码
    async delete(id) {
        try {
            // 检查记录是否存在
            const existing = await this.prisma.taxCode.findUnique({
                where: { id }
            });
            if (!existing) {
                throw new Error('税收编码不存在');
            }
            // 删除记录
            await this.prisma.taxCode.delete({
                where: { id }
            });
            return { success: true };
        }
        catch (error) {
            console.error('删除税收编码失败:', error);
            throw error;
        }
    }
    // 搜索税收编码
    async search(keyword, limit = 20) {
        try {
            const filters = { search: keyword, pageSize: limit };
            const result = await this.findAll(filters);
            return result.items;
        }
        catch (error) {
            console.error('搜索税收编码失败:', error);
            throw new Error('搜索税收编码失败');
        }
    }
    // 批量创建税收编码
    async batchCreate(taxCodes) {
        try {
            const results = {
                success: 0,
                failed: 0,
                errors: []
            };
            for (const taxCode of taxCodes) {
                try {
                    await this.create(taxCode);
                    results.success++;
                }
                catch (error) {
                    results.failed++;
                    results.errors.push(`${taxCode.categoryCode}: ${error instanceof Error ? error.message : '创建失败'}`);
                }
            }
            return results;
        }
        catch (error) {
            console.error('批量创建税收编码失败:', error);
            throw new Error('批量创建税收编码失败');
        }
    }
}
exports.TaxCodeService = TaxCodeService;
//# sourceMappingURL=tax-code.service.js.map