"use strict";
/**
 * 发票员工管理控制器
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvoiceStaffAdminController = void 0;
const base_admin_controller_1 = require("./base-admin.controller");
const response_1 = require("../../../shared/utils/response");
const external_drawer_service_1 = require("../services/external-drawer.service");
const client_1 = require("@prisma/client");
const systemLog_decorator_1 = require("../../../shared/decorators/systemLog.decorator");
class InvoiceStaffAdminController extends base_admin_controller_1.BaseAdminController {
    constructor(adminService) {
        super(adminService);
        const prisma = new client_1.PrismaClient();
        this.externalDrawerService = new external_drawer_service_1.ExternalDrawerService(prisma);
    }
    /**
     * 获取发票员工列表
     */
    async getInvoiceStaffList(req, res) {
        try {
            const { page, pageSize, search, status } = this.getPaginationParams(req);
            const companyId = req.query.companyId ? parseInt(req.query.companyId) : undefined;
            const result = await this.adminService.getInvoiceStaffList({
                page,
                pageSize,
                search,
                status,
                companyId
            });
            res.json((0, response_1.paginationResponse)(result.invoiceStaffs, result.total, page, pageSize, '获取发票员工列表成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 获取发票员工详情
     */
    async getInvoiceStaffDetail(req, res) {
        try {
            const staffId = this.getIdParam(req);
            const staff = await this.adminService.getInvoiceStaffDetail(staffId);
            res.json((0, response_1.successResponse)(staff, '获取发票员工详情成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 创建发票员工
     */
    async createInvoiceStaff(req, res) {
        try {
            const data = req.body;
            const result = await this.adminService.createInvoiceStaff(data);
            res.json((0, response_1.successResponse)(result, '创建发票员工成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 更新发票员工
     */
    async updateInvoiceStaff(req, res) {
        try {
            const staffId = this.getIdParam(req);
            const data = req.body;
            const result = await this.adminService.updateInvoiceStaff(staffId, data);
            res.json((0, response_1.successResponse)(result, '更新发票员工成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 删除发票员工
     */
    async deleteInvoiceStaff(req, res) {
        try {
            const staffId = this.getIdParam(req);
            await this.adminService.deleteInvoiceStaff(staffId);
            res.json((0, response_1.successResponse)(null, '删除发票员工成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 获取发票员工统计信息
     */
    async getInvoiceStaffStats(req, res) {
        try {
            const stats = await this.adminService.getInvoiceStaffStats();
            res.json((0, response_1.successResponse)(stats, '获取发票员工统计信息成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 绑定开票员
     */
    async bindInvoiceStaff(req, res) {
        try {
            const { id } = req.params;
            const staffId = parseInt(id);
            const { companyId } = req.body;
            console.log(`🔄 [控制器] 接收绑定开票员请求: 开票员ID=${staffId}, 公司ID=${companyId}`);
            // 前端校验 - 参数验证
            if (isNaN(staffId)) {
                console.log(`❌ [前端校验] 无效的开票员ID: ${id}`);
                res.status(400).json({
                    success: false,
                    message: '无效的开票员ID',
                    errorType: 'FRONTEND_VALIDATION'
                });
                return;
            }
            if (!companyId) {
                console.log(`❌ [前端校验] 缺少公司ID参数`);
                res.status(400).json({
                    success: false,
                    message: '缺少公司ID参数',
                    errorType: 'FRONTEND_VALIDATION'
                });
                return;
            }
            console.log(`✅ [控制器] 参数校验通过，调用绑定服务`);
            const result = await this.externalDrawerService.bindDrawerWithSync(staffId, parseInt(companyId));
            console.log(`📊 [控制器] 绑定服务返回结果:`, {
                success: result.success,
                message: result.message,
                errorType: result.errorType
            });
            if (result.success) {
                console.log(`🎉 [控制器] 绑定成功`);
                res.status(200).json({
                    success: true,
                    message: result.message,
                    data: result.data
                });
            }
            else {
                // 根据错误类型返回不同的状态码和响应
                let statusCode = 200;
                switch (result.errorType) {
                    case 'FRONTEND_VALIDATION':
                        console.log(`⚠️ [控制器] 前端校验错误: ${result.message}`);
                        statusCode = 400;
                        break;
                    case 'BACKEND_VALIDATION':
                        console.log(`ℹ️ [控制器] 后端校验失败: ${result.message}`);
                        statusCode = 200;
                        break;
                    case 'EXTERNAL_API_BUSINESS':
                        console.log(`⚠️ [控制器] 外部API业务错误: ${result.message}`);
                        statusCode = 200;
                        break;
                    case 'EXTERNAL_API_ERROR':
                        console.log(`💥 [控制器] 外部API网络错误: ${result.message}`);
                        statusCode = 502;
                        break;
                    default:
                        console.log(`❓ [控制器] 未知错误类型: ${result.message}`);
                        statusCode = 500;
                        break;
                }
                console.log(`📤 [控制器] 返回错误响应: 状态码=${statusCode}, 消息=${result.message}`);
                res.status(statusCode).json({
                    success: false,
                    message: result.message,
                    errorType: result.errorType,
                    timestamp: new Date().toISOString()
                });
            }
        }
        catch (error) {
            console.error('💥 [控制器] 绑定开票员异常:', error);
            res.status(500).json({
                success: false,
                message: '服务器内部错误',
                errorType: 'SYSTEM_ERROR',
                timestamp: new Date().toISOString()
            });
        }
    }
    /**
     * 创建开票员
     */
    async createDrawer(req, res) {
        try {
            const { drawer_name, id_number, phone_number, login_pwd } = req.body;
            console.log(`🔄 [控制器] 接收创建开票员请求: ${drawer_name}`);
            // 前端校验 - 参数验证
            if (!drawer_name || !id_number || !phone_number || !login_pwd) {
                console.log(`❌ [前端校验] 缺少必填参数`);
                res.status(400).json({
                    success: false,
                    message: '缺少必填参数：drawer_name, id_number, phone_number, login_pwd',
                    errorType: 'FRONTEND_VALIDATION'
                });
                return;
            }
            // 后端校验 - 身份证号码格式验证
            const idNumberRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
            if (!idNumberRegex.test(id_number)) {
                console.log(`ℹ️ [后端校验] 身份证号码格式不正确: ${id_number}`);
                res.status(200).json({
                    success: false,
                    message: '身份证号码格式不正确',
                    errorType: 'BACKEND_VALIDATION'
                });
                return;
            }
            // 后端校验 - 手机号码格式验证
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(phone_number)) {
                console.log(`ℹ️ [后端校验] 手机号码格式不正确: ${phone_number}`);
                res.status(200).json({
                    success: false,
                    message: '手机号码格式不正确',
                    errorType: 'BACKEND_VALIDATION'
                });
                return;
            }
            console.log(`✅ [控制器] 参数校验通过，调用服务层`);
            const result = await this.externalDrawerService.createDrawer({
                drawer_name,
                id_number,
                phone_number,
                login_pwd
            });
            console.log(`📊 [控制器] 服务层返回结果:`, {
                success: result.success,
                message: result.message,
                errorType: result.errorType
            });
            if (result.success) {
                console.log(`🎉 [控制器] 创建成功`);
                res.status(200).json({
                    success: true,
                    message: result.message,
                    data: {
                        drawerCode: result.drawer_code
                    }
                });
            }
            else {
                // 根据错误类型返回不同的状态码和响应
                let statusCode = 200;
                switch (result.errorType) {
                    case 'FRONTEND_VALIDATION':
                        console.log(`⚠️ [控制器] 前端校验错误: ${result.message}`);
                        statusCode = 400;
                        break;
                    case 'BACKEND_VALIDATION':
                        console.log(`ℹ️ [控制器] 后端校验失败: ${result.message}`);
                        statusCode = 200;
                        break;
                    case 'EXTERNAL_API_BUSINESS':
                        console.log(`⚠️ [控制器] 外部API业务错误: ${result.message}`);
                        statusCode = 200;
                        break;
                    case 'EXTERNAL_API_ERROR':
                        console.log(`💥 [控制器] 外部API网络错误: ${result.message}`);
                        statusCode = 502;
                        break;
                    default:
                        console.log(`❓ [控制器] 未知错误类型: ${result.message}`);
                        statusCode = 500;
                        break;
                }
                console.log(`📤 [控制器] 返回错误响应: 状态码=${statusCode}, 消息=${result.message}`);
                res.status(statusCode).json({
                    success: false,
                    message: result.message,
                    errorType: result.errorType,
                    timestamp: new Date().toISOString()
                });
            }
        }
        catch (error) {
            console.error('💥 [控制器] 创建开票员异常:', error);
            res.status(500).json({
                success: false,
                message: '服务器内部错误',
                errorType: 'SYSTEM_ERROR',
                timestamp: new Date().toISOString()
            });
        }
    }
}
exports.InvoiceStaffAdminController = InvoiceStaffAdminController;
__decorate([
    (0, systemLog_decorator_1.LogView)('INVOICE_STAFF_LIST'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], InvoiceStaffAdminController.prototype, "getInvoiceStaffList", null);
__decorate([
    (0, systemLog_decorator_1.LogView)('INVOICE_STAFF_DETAIL'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], InvoiceStaffAdminController.prototype, "getInvoiceStaffDetail", null);
__decorate([
    (0, systemLog_decorator_1.LogView)('INVOICE_STAFF_CREATE'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], InvoiceStaffAdminController.prototype, "createInvoiceStaff", null);
__decorate([
    (0, systemLog_decorator_1.LogUpdate)('INVOICE_STAFF'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], InvoiceStaffAdminController.prototype, "updateInvoiceStaff", null);
__decorate([
    (0, systemLog_decorator_1.LogDelete)('INVOICE_STAFF'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], InvoiceStaffAdminController.prototype, "deleteInvoiceStaff", null);
__decorate([
    (0, systemLog_decorator_1.LogView)('INVOICE_STAFF_STATS'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], InvoiceStaffAdminController.prototype, "getInvoiceStaffStats", null);
//# sourceMappingURL=invoice-staff-admin.controller.js.map