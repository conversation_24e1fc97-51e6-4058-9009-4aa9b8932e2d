"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createAdminService = createAdminService;
const admin_service_module_1 = require("./admin-service.module");
// 导出服务实例创建函数
function createAdminService(prisma) {
    return new admin_service_module_1.AdminServiceModule(prisma);
}
// 导出所有服务类，方便按需导入
__exportStar(require("./base.service"), exports);
__exportStar(require("./dashboard.service"), exports);
__exportStar(require("./user.service"), exports);
__exportStar(require("./merchant.service"), exports);
__exportStar(require("./company.service"), exports);
__exportStar(require("./invoice.service"), exports);
__exportStar(require("./task.service"), exports);
__exportStar(require("./system-log.service"), exports);
__exportStar(require("./export.service"), exports);
__exportStar(require("./admin-service.module"), exports);
//# sourceMappingURL=index.js.map