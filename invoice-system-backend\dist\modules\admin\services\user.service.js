"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
/**
 * 用户服务
 */
const base_service_1 = require("./base.service");
class UserService extends base_service_1.BaseService {
    /**
     * 获取用户列表
     */
    async getUserList(query) {
        const { page = 1, pageSize = 10, search, status } = query;
        // 构建查询条件
        const where = {};
        if (search) {
            where.OR = [{ username: { contains: search } }, { email: { contains: search } }, { phone: { contains: search } }];
        }
        const [users, total] = await Promise.all([
            this.prisma.user.findMany({
                where,
                skip: (page - 1) * pageSize,
                take: pageSize,
                orderBy: { createdAt: 'desc' },
                select: {
                    id: true,
                    username: true,
                    email: true,
                    phone: true,
                    status: true,
                    membershipLevel: true,
                    membershipExpiry: true,
                    totalInvoices: true,
                    totalAmount: true,
                    createdAt: true,
                    updatedAt: true,
                    _count: {
                        select: {
                            invoices: true
                        }
                    }
                }
            }),
            this.prisma.user.count({ where })
        ]);
        // 格式化返回数据
        const formattedUsers = users.map(user => ({
            id: user.id,
            username: user.username || '',
            email: user.email || '',
            phone: user.phone || '',
            status: user.status,
            membershipLevel: user.membershipLevel || '普通会员',
            membershipExpiry: user.membershipExpiry ? user.membershipExpiry.toISOString().split('T')[0] : null,
            totalInvoices: user._count.invoices,
            totalAmount: Number(user.totalAmount || 0),
            registeredAt: user.createdAt, // 注册时间使用创建时间
            lastLoginAt: null, // 暂时设为null，后续可以添加字段
            lastLoginIp: null,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt
        }));
        return { users: formattedUsers, total };
    }
    /**
     * 获取用户详情
     */
    async getUserDetail(id) {
        const user = await this.prisma.user.findUnique({
            where: { id },
            select: {
                id: true,
                username: true,
                email: true,
                phone: true,
                status: true,
                membershipLevel: true,
                membershipExpiry: true,
                totalInvoices: true,
                totalAmount: true,
                createdAt: true,
                updatedAt: true,
                _count: {
                    select: {
                        invoices: true
                    }
                }
            }
        });
        if (!user) {
            throw new Error('用户不存在');
        }
        return {
            id: user.id,
            username: user.username || '',
            email: user.email || '',
            phone: user.phone || '',
            status: user.status,
            membershipLevel: user.membershipLevel || '普通会员',
            membershipExpiry: user.membershipExpiry ? user.membershipExpiry.toISOString().split('T')[0] : null,
            totalInvoices: user._count.invoices,
            totalAmount: Number(user.totalAmount || 0),
            registeredAt: user.createdAt,
            lastLoginAt: null,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt
        };
    }
    /**
     * 创建用户
     */
    async createUser(data) {
        const { username, email, phone, password } = data;
        // 检查用户名是否已存在
        if (username) {
            const existingUser = await this.prisma.user.findFirst({
                where: { username }
            });
            if (existingUser) {
                throw new Error('用户名已存在');
            }
        }
        // 检查邮箱是否已存在
        if (email) {
            const existingEmail = await this.prisma.user.findFirst({
                where: { email }
            });
            if (existingEmail) {
                throw new Error('邮箱已存在');
            }
        }
        // 检查手机号是否已存在
        if (phone) {
            const existingPhone = await this.prisma.user.findFirst({
                where: { phone }
            });
            if (existingPhone) {
                throw new Error('手机号已存在');
            }
        }
        // 创建用户
        const user = await this.prisma.user.create({
            data: {
                username: username || null,
                email: email || null,
                phone: phone || null,
                password: password, // 在实际应用中应该加密密码
                status: 'ACTIVE',
                totalInvoices: 0
            }
        });
        return {
            id: user.id,
            username: user.username || '',
            email: user.email || '',
            phone: user.phone || '',
            status: user.status,
            membershipLevel: '普通会员',
            membershipExpiry: null,
            totalInvoices: 0,
            totalAmount: 0,
            registeredAt: user.createdAt,
            lastLoginAt: null,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt
        };
    }
    /**
     * 更新用户
     */
    async updateUser(id, data) {
        const { username, email, phone, password, membershipLevel, membershipExpiry } = data;
        // 检查用户是否存在
        const existingUser = await this.prisma.user.findUnique({
            where: { id }
        });
        if (!existingUser) {
            throw new Error('用户不存在');
        }
        // 检查用户名是否已被其他用户使用
        if (username && username !== existingUser.username) {
            const duplicateUsername = await this.prisma.user.findFirst({
                where: {
                    username,
                    id: { not: id }
                }
            });
            if (duplicateUsername) {
                throw new Error('用户名已存在');
            }
        }
        // 检查邮箱是否已被其他用户使用
        if (email && email !== existingUser.email) {
            const duplicateEmail = await this.prisma.user.findFirst({
                where: {
                    email,
                    id: { not: id }
                }
            });
            if (duplicateEmail) {
                throw new Error('邮箱已存在');
            }
        }
        // 检查手机号是否已被其他用户使用
        if (phone && phone !== existingUser.phone) {
            const duplicatePhone = await this.prisma.user.findFirst({
                where: {
                    phone,
                    id: { not: id }
                }
            });
            if (duplicatePhone) {
                throw new Error('手机号已存在');
            }
        }
        // 构建更新数据
        const updateData = {
            username: username || null,
            email: email || null,
            phone: phone || null
        };
        // 如果提供了新密码，更新密码
        if (password) {
            updateData.password = password; // 在实际应用中应该加密密码
        }
        // 如果提供了会员等级，更新会员等级
        if (membershipLevel) {
            updateData.membershipLevel = membershipLevel;
        }
        // 如果提供了会员有效期，更新会员有效期
        if (membershipExpiry) {
            updateData.membershipExpiry = new Date(membershipExpiry);
        }
        else if (membershipLevel === '普通会员') {
            // 如果设置为普通会员，清空有效期
            updateData.membershipExpiry = null;
        }
        // 更新用户
        const user = await this.prisma.user.update({
            where: { id },
            data: updateData
        });
        // 返回用户信息，包含会员等级和有效期
        return {
            id: user.id,
            username: user.username || '',
            email: user.email || '',
            phone: user.phone || '',
            status: user.status,
            membershipLevel: user.membershipLevel,
            membershipExpiry: user.membershipExpiry ? user.membershipExpiry.toISOString().split('T')[0] : null,
            totalInvoices: Number(user.totalInvoices || 0),
            totalAmount: Number(user.totalAmount || 0),
            registeredAt: user.createdAt,
            lastLoginAt: null,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt
        };
    }
    /**
     * 删除用户
     */
    async deleteUser(id) {
        // 检查用户是否存在
        const existingUser = await this.prisma.user.findUnique({
            where: { id }
        });
        if (!existingUser) {
            throw new Error('用户不存在');
        }
        // 删除用户
        await this.prisma.user.delete({
            where: { id }
        });
    }
    /**
     * 获取用户统计信息
     */
    async getUserStats() {
        const { thisMonth } = this.getDateRanges();
        const [totalUsers, activeUsers, bannedUsers, newUsersThisMonth] = await Promise.all([
            // 总用户数
            this.prisma.user.count(),
            // 正常状态用户数
            this.prisma.user.count({
                where: { status: 'ACTIVE' }
            }),
            // 禁用用户数
            this.prisma.user.count({
                where: { status: 'BANNED' }
            }),
            // 本月新增用户数
            this.prisma.user.count({
                where: {
                    createdAt: {
                        gte: thisMonth
                    }
                }
            })
        ]);
        return {
            totalInvoices: 0,
            totalAmount: 0,
            monthlyInvoices: 0,
            monthlyAmount: 0,
            pendingInvoices: 0,
            recentInvoices: []
        };
    }
}
exports.UserService = UserService;
//# sourceMappingURL=user.service.js.map