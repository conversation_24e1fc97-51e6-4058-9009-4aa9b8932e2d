"use strict";
/**
 * 发票管理控制器
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvoiceAdminController = void 0;
const base_admin_controller_1 = require("./base-admin.controller");
const response_1 = require("../../../shared/utils/response");
const errorHandler_1 = require("../../../shared/middleware/errorHandler");
const systemLog_decorator_1 = require("../../../shared/decorators/systemLog.decorator");
class InvoiceAdminController extends base_admin_controller_1.BaseAdminController {
    /**
     * 获取发票列表
     */
    async getInvoiceList(req, res) {
        try {
            const { page, pageSize, search, status } = this.getPaginationParams(req);
            const type = req.query.type;
            const merchantId = req.query.merchantId ? parseInt(req.query.merchantId) : undefined;
            const companyId = req.query.companyId ? parseInt(req.query.companyId) : undefined;
            const result = await this.adminService.getInvoiceList({
                page,
                pageSize,
                search,
                status,
                type,
                merchantId,
                companyId
            });
            res.json((0, response_1.paginationResponse)(result.invoices, result.total, page, pageSize, '获取发票列表成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 获取发票详情
     */
    async getInvoiceDetail(req, res) {
        try {
            const invoiceId = this.getIdParam(req);
            const invoice = await this.adminService.getInvoiceDetail(invoiceId);
            res.json((0, response_1.successResponse)(invoice, '获取发票详情成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 更新发票状态
     */
    async updateInvoiceStatus(req, res) {
        try {
            const invoiceId = this.getIdParam(req);
            const { status, reason } = req.body;
            if (!status) {
                throw new errorHandler_1.AppError('状态参数不能为空', 400);
            }
            const result = await this.adminService.updateInvoiceStatus(invoiceId, status);
            res.json((0, response_1.successResponse)(result, '发票状态更新成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 创建发票
     */
    async createInvoice(req, res) {
        try {
            const invoiceData = req.body;
            // 验证必填字段
            if (!invoiceData.buyerName || !invoiceData.buyerTaxNumber) {
                throw new errorHandler_1.AppError('购买方名称和纳税人识别号不能为空', 400);
            }
            if (!invoiceData.totalAmount || invoiceData.totalAmount <= 0) {
                throw new errorHandler_1.AppError('发票金额必须大于0', 400);
            }
            const result = await this.adminService.createInvoice(invoiceData);
            res.json((0, response_1.successResponse)(result, '发票创建成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 更新发票
     */
    async updateInvoice(req, res) {
        try {
            const invoiceId = this.getIdParam(req);
            const updateData = req.body;
            const result = await this.adminService.updateInvoice(invoiceId, updateData);
            res.json((0, response_1.successResponse)(result, '发票更新成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 删除发票
     */
    async deleteInvoice(req, res) {
        try {
            const invoiceId = this.getIdParam(req);
            const result = await this.adminService.deleteInvoice(invoiceId);
            res.json((0, response_1.successResponse)(result, '发票删除成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 获取发票统计信息
     */
    async getInvoiceStats(req, res) {
        try {
            const stats = await this.adminService.getInvoiceStaffStats();
            res.json((0, response_1.successResponse)(stats, '获取发票统计信息成功'));
        }
        catch (error) {
            throw error;
        }
    }
}
exports.InvoiceAdminController = InvoiceAdminController;
__decorate([
    (0, systemLog_decorator_1.LogView)('发票管理'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], InvoiceAdminController.prototype, "getInvoiceList", null);
__decorate([
    (0, systemLog_decorator_1.LogView)('发票管理'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], InvoiceAdminController.prototype, "getInvoiceDetail", null);
__decorate([
    (0, systemLog_decorator_1.LogUpdate)('发票管理'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], InvoiceAdminController.prototype, "updateInvoiceStatus", null);
__decorate([
    (0, systemLog_decorator_1.LogUpdate)('发票管理'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], InvoiceAdminController.prototype, "createInvoice", null);
__decorate([
    (0, systemLog_decorator_1.LogUpdate)('发票管理'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], InvoiceAdminController.prototype, "updateInvoice", null);
__decorate([
    (0, systemLog_decorator_1.LogUpdate)('发票管理'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], InvoiceAdminController.prototype, "deleteInvoice", null);
__decorate([
    (0, systemLog_decorator_1.LogView)('发票统计'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], InvoiceAdminController.prototype, "getInvoiceStats", null);
//# sourceMappingURL=invoice-admin.controller.js.map