/**
 * 管理员服务接口定义
 */
import { DashboardStats, MerchantStats, UserStats, PaginationQuery, SystemLogListQuery } from '../../../shared/interfaces/types';
export interface IAdminService {
    /**
     * 获取仪表板统计数据
     */
    getDashboardStats(): Promise<DashboardStats>;
    /**
     * 获取用户列表
     */
    getUserList(query: PaginationQuery & {
        search?: string;
        status?: string;
    }): Promise<{
        users: any[];
        total: number;
    }>;
    /**
     * 获取用户详情
     */
    getUserDetail(id: number): Promise<any>;
    /**
     * 创建用户
     */
    createUser(data: any): Promise<any>;
    /**
     * 更新用户
     */
    updateUser(id: number, data: any): Promise<any>;
    /**
     * 删除用户
     */
    deleteUser(id: number): Promise<void>;
    /**
     * 获取商家列表
     */
    getMerchantList(query: PaginationQuery & {
        search?: string;
        status?: string;
        membershipLevel?: string;
    }): Promise<{
        merchants: any[];
        total: number;
    }>;
    /**
     * 获取商家详情
     */
    getMerchantDetail(id: number): Promise<any>;
    /**
     * 创建商家
     */
    createMerchant(data: any): Promise<any>;
    /**
     * 更新商家
     */
    updateMerchant(id: number, data: any): Promise<any>;
    /**
     * 删除商家
     */
    deleteMerchant(id: number): Promise<{
        success: boolean;
        message?: string;
        data?: any;
    }>;
    /**
     * 获取公司列表
     */
    getCompanyList(query: PaginationQuery & {
        search?: string;
        status?: string;
        merchantId?: number;
    }): Promise<{
        companies: any[];
        total: number;
    }>;
    /**
     * 获取公司详情
     */
    getCompanyDetail(id: number): Promise<any>;
    /**
     * 创建公司
     */
    createCompany(data: any): Promise<any>;
    /**
     * 更新公司
     */
    updateCompany(id: number, data: any): Promise<any>;
    /**
     * 删除公司
     */
    deleteCompany(id: number): Promise<void>;
    /**
     * 获取发票列表
     */
    getInvoiceList(query: PaginationQuery & {
        search?: string;
        status?: string;
        type?: string;
        merchantId?: number;
        companyId?: number;
    }): Promise<{
        invoices: any[];
        total: number;
    }>;
    /**
     * 获取发票详情
     */
    getInvoiceDetail(id: number): Promise<any>;
    /**
     * 创建发票
     */
    createInvoice(data: any): Promise<any>;
    /**
     * 更新发票
     */
    updateInvoice(id: number, data: any): Promise<any>;
    /**
     * 删除发票
     */
    deleteInvoice(id: number): Promise<any>;
    /**
     * 更新发票状态
     */
    updateInvoiceStatus(id: number, status: string): Promise<any>;
    /**
     * 获取任务列表
     */
    getTaskList(query: PaginationQuery & {
        search?: string;
        status?: string;
        type?: string;
    }): Promise<{
        tasks: any[];
        total: number;
    }>;
    /**
     * 获取系统日志列表
     */
    getSystemLogList(query: SystemLogListQuery): Promise<{
        logs: any[];
        total: number;
    }>;
    /**
     * 创建系统日志
     */
    createSystemLog(data: any): Promise<any>;
    /**
     * 获取系统日志详情
     */
    getSystemLogDetail(id: number): Promise<any>;
    /**
     * 获取系统日志统计信息
     */
    getSystemLogStats(): Promise<any>;
    /**
     * 获取操作统计
     */
    getActionStats(days: number): Promise<any>;
    /**
     * 获取日志级别统计
     */
    getLogLevelStats(days: number): Promise<any>;
    /**
     * 获取用户操作排行
     */
    getUserActionRanking(days: number, userType?: string): Promise<any>;
    /**
     * 获取日志趋势数据
     */
    getLogTrends(days: number, level?: string): Promise<any>;
    /**
     * 清理过期日志
     */
    cleanupOldLogs(days: number): Promise<{
        deletedCount: number;
    }>;
    /**
     * 获取商家统计信息
     */
    getMerchantStats(): Promise<MerchantStats>;
    /**
     * 获取用户统计信息
     */
    getUserStats(): Promise<UserStats>;
    /**
     * 获取公司统计信息
     */
    getCompanyStats(): Promise<any>;
    /**
     * 获取开票员列表
     */
    getInvoiceStaffList(query: PaginationQuery & {
        search?: string;
        status?: string;
        merchantId?: number;
        companyId?: number;
    }): Promise<{
        invoiceStaffs: any[];
        total: number;
    }>;
    /**
     * 获取开票员详情
     */
    getInvoiceStaffDetail(id: number): Promise<any>;
    /**
     * 创建开票员
     */
    createInvoiceStaff(data: any): Promise<any>;
    /**
     * 更新开票员
     */
    updateInvoiceStaff(id: number, data: any): Promise<any>;
    /**
     * 删除开票员
     */
    deleteInvoiceStaff(id: number): Promise<void>;
    /**
     * 获取开票员统计信息
     */
    getInvoiceStaffStats(): Promise<any>;
    /**
     * 导出数据
     */
    exportData(type: string, format: string, filters: any): Promise<{
        data: any[];
        filename: string;
        headers: string[];
    }>;
}
//# sourceMappingURL=admin-service.interface.d.ts.map