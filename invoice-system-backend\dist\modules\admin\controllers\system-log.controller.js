"use strict";
/**
 * 系统日志控制器
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemLogController = void 0;
const base_admin_controller_1 = require("./base-admin.controller");
const response_1 = require("../../../shared/utils/response");
const errorHandler_1 = require("../../../shared/middleware/errorHandler");
class SystemLogController extends base_admin_controller_1.BaseAdminController {
    /**
     * 获取系统日志列表
     */
    async getSystemLogList(req, res) {
        try {
            const { page, pageSize, search } = this.getPaginationParams(req);
            const userType = req.query.userType;
            const action = req.query.action;
            const resource = req.query.resource;
            const level = req.query.level;
            const startDate = req.query.startDate;
            const endDate = req.query.endDate;
            const result = await this.adminService.getSystemLogList({
                page,
                pageSize,
                search,
                userType: userType,
                action,
                resource,
                level,
                startDate,
                endDate
            });
            res.json((0, response_1.paginationResponse)(result.logs, result.total, page, pageSize, '获取系统日志列表成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 获取系统日志详情
     */
    async getSystemLogDetail(req, res) {
        try {
            const id = parseInt(req.params.id);
            if (isNaN(id)) {
                throw new errorHandler_1.AppError('无效的日志ID', 400);
            }
            const log = await this.adminService.getSystemLogDetail(id);
            res.json((0, response_1.successResponse)(log, '获取日志详情成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 获取系统日志统计信息
     */
    async getSystemLogStats(req, res) {
        try {
            const stats = await this.adminService.getSystemLogStats();
            res.json((0, response_1.successResponse)(stats, '获取日志统计成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 获取操作统计
     */
    async getActionStats(req, res) {
        try {
            const days = parseInt(req.query.days) || 7;
            const stats = await this.adminService.getActionStats(days);
            res.json((0, response_1.successResponse)(stats, '获取操作统计成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 清理过期日志
     */
    async cleanupOldLogs(req, res) {
        try {
            const days = parseInt(req.body.days) || 90;
            if (days < 30) {
                throw new errorHandler_1.AppError('保留天数不能少于30天', 400);
            }
            const result = await this.adminService.cleanupOldLogs(days);
            res.json((0, response_1.successResponse)(result, `成功清理 ${result.deletedCount} 条过期日志`));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 获取日志级别统计
     */
    async getLogLevelStats(req, res) {
        try {
            const days = parseInt(req.query.days) || 7;
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - days);
            // 这里可以调用服务层方法获取级别统计
            const stats = await this.adminService.getLogLevelStats(days);
            res.json((0, response_1.successResponse)(stats, '获取日志级别统计成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 获取用户操作排行
     */
    async getUserActionRanking(req, res) {
        try {
            const days = parseInt(req.query.days) || 7;
            const userType = req.query.userType;
            const ranking = await this.adminService.getUserActionRanking(days, userType);
            res.json((0, response_1.successResponse)(ranking, '获取用户操作排行成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 获取系统日志趋势数据
     */
    async getLogTrends(req, res) {
        try {
            const days = parseInt(req.query.days) || 30;
            const level = req.query.level;
            const trends = await this.adminService.getLogTrends(days, level);
            res.json((0, response_1.successResponse)(trends, '获取日志趋势数据成功'));
        }
        catch (error) {
            throw error;
        }
    }
}
exports.SystemLogController = SystemLogController;
//# sourceMappingURL=system-log.controller.js.map