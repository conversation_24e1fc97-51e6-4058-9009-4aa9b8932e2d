"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompanyService = void 0;
/**
 * 公司服务
 */
const base_service_1 = require("./base.service");
class CompanyService extends base_service_1.BaseService {
    /**
     * 获取公司列表
     */
    async getCompanies(params = {}) {
        const { page = 1, pageSize = 10, limit, search, status, region, merchantId } = params;
        // 兼容不同的分页参数名
        const actualLimit = pageSize || limit || 10;
        const skip = (page - 1) * actualLimit;
        const where = {};
        if (search) {
            where.OR = [
                { name: { contains: search } },
                { taxNumber: { contains: search } },
                { legalRepresentativeName: { contains: search } }
            ];
        }
        if (status) {
            where.status = status;
        }
        if (region) {
            where.region = region;
        }
        if (merchantId) {
            where.merchantId = merchantId;
        }
        const [companies, total] = await Promise.all([
            this.prisma.company.findMany({
                where,
                skip,
                take: actualLimit,
                include: {
                    merchant: {
                        select: {
                            id: true,
                            username: true
                        }
                    }
                },
                orderBy: { createdAt: 'desc' }
            }),
            this.prisma.company.count({ where })
        ]);
        console.log(`🔍 获取公司列表: 查询到 ${companies.length} 条记录，总计 ${total} 条`);
        return {
            companies,
            total
        };
    }
    /**
     * 根据ID获取公司
     */
    async getCompanyById(id) {
        const company = await this.prisma.company.findUnique({
            where: { id },
            include: {
                merchant: {
                    select: {
                        id: true,
                        username: true
                    }
                }
            }
        });
        if (!company) {
            throw new Error('公司不存在');
        }
        return company;
    }
    /**
     * 创建公司
     */
    async createCompany(data) {
        console.log('🆕 创建公司');
        console.log('📝 创建数据:', JSON.stringify(data, null, 2));
        try {
            const company = await this.prisma.company.create({
                data: {
                    ...data,
                    validityPeriod: data.validityPeriod ? new Date(data.validityPeriod) : null,
                },
                include: {
                    merchant: {
                        select: {
                            id: true,
                            username: true
                        }
                    }
                }
            });
            console.log('✅ 公司创建成功:', company.name);
            return company;
        }
        catch (error) {
            console.error('❌ 创建公司失败:', error);
            throw error;
        }
    }
    /**
     * 更新公司
     */
    async updateCompany(id, data) {
        console.log('🔄 更新公司，ID:', id);
        console.log('📝 更新数据:', JSON.stringify(data, null, 2));
        try {
            // 动态构建更新数据，只包含提供的字段
            const updateData = {};
            // 基本字段
            if (data.merchantId !== undefined)
                updateData.merchantId = data.merchantId;
            if (data.name !== undefined)
                updateData.name = data.name;
            if (data.taxNumber !== undefined)
                updateData.taxNumber = data.taxNumber;
            if (data.legalRepresentativeName !== undefined)
                updateData.legalRepresentativeName = data.legalRepresentativeName;
            if (data.legalRepresentativeIdCardNumber !== undefined)
                updateData.legalRepresentativeIdCardNumber = data.legalRepresentativeIdCardNumber;
            if (data.region !== undefined)
                updateData.region = data.region;
            if (data.regionCode !== undefined)
                updateData.regionCode = data.regionCode;
            if (data.status !== undefined)
                updateData.status = data.status;
            if (data.externalCompanyId !== undefined)
                updateData.externalCompanyId = data.externalCompanyId;
            // 日期字段特殊处理 - 空字符串转为 null
            if (data.validityPeriod !== undefined) {
                if (data.validityPeriod && data.validityPeriod.trim() !== '') {
                    // 尝试解析日期
                    const date = new Date(data.validityPeriod);
                    if (!isNaN(date.getTime())) {
                        updateData.validityPeriod = date;
                    }
                    else {
                        updateData.validityPeriod = null;
                    }
                }
                else {
                    // 空字符串或 null/undefined 都设为 null
                    updateData.validityPeriod = null;
                }
            }
            console.log('🔧 处理后的更新数据:', JSON.stringify(updateData, null, 2));
            const company = await this.prisma.company.update({
                where: { id },
                data: updateData,
                include: {
                    merchant: {
                        select: {
                            id: true,
                            username: true
                        }
                    }
                }
            });
            console.log('✅ 公司更新成功:', company.name);
            return company;
        }
        catch (error) {
            console.error('❌ 更新公司失败:', error);
            throw error;
        }
    }
    /**
     * 删除公司
     */
    async deleteCompany(id) {
        console.log('🗑️ 删除公司，ID:', id);
        try {
            // 检查是否有关联的开票员
            const staffCount = await this.prisma.invoiceStaff.count({
                where: { companyId: id }
            });
            if (staffCount > 0) {
                throw new Error('该公司下还有开票员，无法删除');
            }
            // 检查是否有关联的发票
            const invoiceCount = await this.prisma.invoice.count({
                where: { companyId: id }
            });
            if (invoiceCount > 0) {
                throw new Error('该公司下还有发票记录，无法删除');
            }
            const company = await this.prisma.company.delete({
                where: { id }
            });
            console.log('✅ 公司删除成功:', company.name);
            return company;
        }
        catch (error) {
            console.error('❌ 删除公司失败:', error);
            throw error;
        }
    }
    /**
     * 获取公司统计信息
     */
    async getCompanyStats() {
        const [total, active, inactive, suspended] = await Promise.all([
            this.prisma.company.count(),
            this.prisma.company.count({ where: { status: 'ACTIVE' } }),
            this.prisma.company.count({ where: { status: 'INACTIVE' } }),
            this.prisma.company.count({ where: { status: 'SUSPENDED' } })
        ]);
        return {
            totalCompanies: total,
            statusStats: {
                ACTIVE: active,
                INACTIVE: inactive,
                SUSPENDED: suspended
            }
        };
    }
}
exports.CompanyService = CompanyService;
//# sourceMappingURL=company.service.js.map