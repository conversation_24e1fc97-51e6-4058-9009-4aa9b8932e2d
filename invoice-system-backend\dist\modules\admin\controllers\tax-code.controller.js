"use strict";
/**
 * 税收编码管理控制器
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaxCodeController = void 0;
const base_admin_controller_1 = require("./base-admin.controller");
const response_1 = require("../../../shared/utils/response");
const tax_code_service_1 = require("../services/tax-code.service");
class TaxCodeController extends base_admin_controller_1.BaseAdminController {
    constructor(adminService, prisma) {
        super(adminService);
        this.taxCodeService = new tax_code_service_1.TaxCodeService(prisma);
    }
    async getTaxCodes(req, res) {
        try {
            const { page, pageSize, search, status } = this.getPaginationParams(req);
            const { isActive, isSummary } = req.query;
            const filters = {
                page,
                pageSize,
                search,
                ...(isActive !== undefined && { isActive: isActive === 'true' }),
                ...(isSummary !== undefined && { isSummary: isSummary === 'true' })
            };
            const result = await this.taxCodeService.findAll(filters);
            res.json((0, response_1.paginationResponse)(result.items, result.total, result.page, result.pageSize, '获取税收编码列表成功'));
        }
        catch (error) {
            console.error('获取税收编码列表失败:', error);
            res.status(500).json((0, response_1.errorResponse)('获取税收编码列表失败'));
        }
    }
    async getTaxCode(req, res) {
        try {
            const id = this.getIdParam(req);
            const taxCode = await this.taxCodeService.findById(id);
            if (!taxCode) {
                res.status(404).json((0, response_1.errorResponse)('税收编码不存在'));
                return;
            }
            res.json((0, response_1.successResponse)(taxCode, '获取税收编码详情成功'));
        }
        catch (error) {
            console.error('获取税收编码详情失败:', error);
            res.status(500).json((0, response_1.errorResponse)('获取税收编码详情失败'));
        }
    }
    async createTaxCode(req, res) {
        try {
            const taxCodeData = req.body;
            // 基础验证
            if (!taxCodeData.categoryName || !taxCodeData.categoryCode) {
                res.status(400).json((0, response_1.errorResponse)('分类名称和分类编号为必填项'));
                return;
            }
            const taxCode = await this.taxCodeService.create(taxCodeData);
            res.status(201).json((0, response_1.successResponse)(taxCode, '创建税收编码成功'));
        }
        catch (error) {
            console.error('创建税收编码失败:', error);
            if (error?.message?.includes('已存在')) {
                res.status(400).json((0, response_1.errorResponse)(error.message));
            }
            else {
                res.status(500).json((0, response_1.errorResponse)('创建税收编码失败'));
            }
        }
    }
    async updateTaxCode(req, res) {
        try {
            const id = this.getIdParam(req);
            const updateData = req.body;
            const taxCode = await this.taxCodeService.update(id, updateData);
            res.json((0, response_1.successResponse)(taxCode, '更新税收编码成功'));
        }
        catch (error) {
            console.error('更新税收编码失败:', error);
            if (error?.message?.includes('不存在')) {
                res.status(404).json((0, response_1.errorResponse)(error.message));
            }
            else if (error?.message?.includes('已存在')) {
                res.status(400).json((0, response_1.errorResponse)(error.message));
            }
            else {
                res.status(500).json((0, response_1.errorResponse)('更新税收编码失败'));
            }
        }
    }
    async deleteTaxCode(req, res) {
        try {
            const id = this.getIdParam(req);
            await this.taxCodeService.delete(id);
            res.json((0, response_1.successResponse)(null, '删除税收编码成功'));
        }
        catch (error) {
            console.error('删除税收编码失败:', error);
            if (error?.message?.includes('不存在')) {
                res.status(404).json((0, response_1.errorResponse)(error.message));
            }
            else {
                res.status(500).json((0, response_1.errorResponse)('删除税收编码失败'));
            }
        }
    }
    async searchTaxCodes(req, res) {
        try {
            const { keyword, limit = '20' } = req.query;
            if (!keyword) {
                res.status(400).json((0, response_1.errorResponse)('搜索关键字不能为空'));
                return;
            }
            const results = await this.taxCodeService.search(keyword, parseInt(limit));
            res.json((0, response_1.successResponse)(results, '搜索税收编码成功'));
        }
        catch (error) {
            console.error('搜索税收编码失败:', error);
            res.status(500).json((0, response_1.errorResponse)('搜索税收编码失败'));
        }
    }
    async importTaxCodes(req, res) {
        try {
            // 这里应该处理文件上传和解析
            // 目前返回模拟结果
            const result = {
                success: 100,
                failed: 0,
                errors: []
            };
            res.json((0, response_1.successResponse)(result, '导入税收编码成功'));
        }
        catch (error) {
            console.error('导入税收编码失败:', error);
            res.status(500).json((0, response_1.errorResponse)('导入税收编码失败'));
        }
    }
    async exportTaxCodes(req, res) {
        try {
            // 这里应该生成实际的Excel文件
            // 目前返回模拟文件
            res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            res.setHeader('Content-Disposition', 'attachment; filename=tax-codes.xlsx');
            res.send(Buffer.from('模拟Excel文件内容'));
        }
        catch (error) {
            console.error('导出税收编码失败:', error);
            res.status(500).json((0, response_1.errorResponse)('导出税收编码失败'));
        }
    }
    async getTaxCodeStats(req, res) {
        try {
            // 获取统计信息
            const stats = {
                total: 0,
                active: 0,
                inactive: 0,
                summary: 0
            };
            res.json((0, response_1.successResponse)(stats, '获取税收编码统计信息成功'));
        }
        catch (error) {
            console.error('获取税收编码统计信息失败:', error);
            res.status(500).json((0, response_1.errorResponse)('获取税收编码统计信息失败'));
        }
    }
    async updateTaxCodeStatus(req, res) {
        try {
            const id = this.getIdParam(req);
            const { isActive } = req.body;
            if (typeof isActive !== 'boolean') {
                res.status(400).json((0, response_1.errorResponse)('状态值必须为布尔类型'));
                return;
            }
            const taxCode = await this.taxCodeService.update(id, { isActive });
            res.json((0, response_1.successResponse)(taxCode, '更新税收编码状态成功'));
        }
        catch (error) {
            console.error('更新税收编码状态失败:', error);
            if (error?.message?.includes('不存在')) {
                res.status(404).json((0, response_1.errorResponse)(error.message));
            }
            else {
                res.status(500).json((0, response_1.errorResponse)('更新税收编码状态失败'));
            }
        }
    }
}
exports.TaxCodeController = TaxCodeController;
//# sourceMappingURL=tax-code.controller.js.map