/**
 * 商家管理控制器
 */
import { Request, Response } from 'express';
import { BaseAdminController } from './base-admin.controller';
export declare class MerchantAdminController extends BaseAdminController {
    getMerchantList(req: Request, res: Response): Promise<void>;
    getMerchantDetail(req: Request, res: Response): Promise<void>;
    createMerchant(req: Request, res: Response): Promise<void>;
    updateMerchant(req: Request, res: Response): Promise<void>;
    deleteMerchant(req: Request, res: Response): Promise<void>;
    updateMerchantStatus(req: Request, res: Response): Promise<void>;
    getMerchantStats(req: Request, res: Response): Promise<void>;
}
//# sourceMappingURL=merchant-admin.controller.d.ts.map