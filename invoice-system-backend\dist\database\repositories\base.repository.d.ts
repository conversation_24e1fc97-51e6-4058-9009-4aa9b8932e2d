/**
 * 基础仓库类
 * 提供通用的数据库操作方法
 */
import { PrismaClient } from '@prisma/client';
export declare abstract class BaseRepository {
    protected prisma: PrismaClient;
    constructor(prisma: PrismaClient);
    /**
     * 构建分页查询参数
     */
    protected buildPaginationParams(page: number, pageSize: number): {
        skip: number;
        take: number;
    };
    /**
     * 构建排序参数
     */
    protected buildOrderBy(sortBy: string, sortOrder?: 'asc' | 'desc'): any;
    /**
     * 构建搜索条件
     */
    protected buildSearchCondition(search: string, fields: string[]): {
        OR?: undefined;
    } | {
        OR: {
            [x: string]: {
                contains: string;
            };
        }[];
    };
    /**
     * 构建日期范围条件
     */
    protected buildDateRangeCondition(startDate?: string, endDate?: string): any;
    /**
     * 构建数值范围条件
     */
    protected buildNumberRangeCondition(min?: number, max?: number): any;
}
//# sourceMappingURL=base.repository.d.ts.map