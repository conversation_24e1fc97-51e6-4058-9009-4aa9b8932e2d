/**
 * 用户管理控制器
 */
import { Request, Response } from 'express';
import { BaseAdminController } from './base-admin.controller';
export declare class UserAdminController extends BaseAdminController {
    /**
     * 获取用户列表
     */
    getUserList(req: Request, res: Response): Promise<void>;
    /**
     * 获取用户详情
     */
    getUserDetail(req: Request, res: Response): Promise<void>;
    /**
     * 创建用户
     */
    createUser(req: Request, res: Response): Promise<void>;
    /**
     * 更新用户
     */
    updateUser(req: Request, res: Response): Promise<void>;
    /**
     * 删除用户
     */
    deleteUser(req: Request, res: Response): Promise<void>;
    /**
     * 获取用户统计信息
     */
    getUserStats(req: Request, res: Response): Promise<void>;
}
//# sourceMappingURL=user-admin.controller.d.ts.map