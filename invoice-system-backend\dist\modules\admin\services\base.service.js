"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseService = void 0;
class BaseService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    /**
     * 获取日期范围
     */
    getDateRanges() {
        // 获取今天开始时间
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        // 获取本月开始时间
        const thisMonth = new Date();
        thisMonth.setDate(1);
        thisMonth.setHours(0, 0, 0, 0);
        return { today, thisMonth };
    }
}
exports.BaseService = BaseService;
//# sourceMappingURL=base.service.js.map