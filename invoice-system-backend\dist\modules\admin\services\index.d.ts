/**
 * 管理员服务模块入口文件
 */
import { PrismaClient } from '@prisma/client';
import { AdminServiceModule } from './admin-service.module';
export declare function createAdminService(prisma: PrismaClient): AdminServiceModule;
export * from './base.service';
export * from './dashboard.service';
export * from './user.service';
export * from './merchant.service';
export * from './company.service';
export * from './invoice.service';
export * from './task.service';
export * from './system-log.service';
export * from './export.service';
export * from './admin-service.module';
//# sourceMappingURL=index.d.ts.map