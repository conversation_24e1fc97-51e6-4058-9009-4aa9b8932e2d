"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemLogService = void 0;
/**
 * 系统日志服务
 */
const base_service_1 = require("./base.service");
class SystemLogService extends base_service_1.BaseService {
    /**
     * 获取系统日志列表
     */
    async getSystemLogList(query) {
        const { page = 1, pageSize = 10, search, userType, action, resource, level, startDate, endDate } = query;
        // 构建查询条件
        const where = {};
        if (search) {
            where.OR = [
                { action: { contains: search } },
                { resource: { contains: search } },
                { ip: { contains: search } },
                { admin: { username: { contains: search } } },
                { merchant: { username: { contains: search } } },
                { user: { username: { contains: search } } }
            ];
        }
        if (userType && userType !== 'all') {
            where.userType = userType;
        }
        if (action && action !== 'all') {
            where.action = action;
        }
        if (resource && resource !== 'all') {
            where.resource = resource;
        }
        if (level && level !== 'all') {
            where.level = level;
        }
        // 时间范围筛选
        if (startDate || endDate) {
            where.createdAt = {};
            if (startDate) {
                where.createdAt.gte = new Date(startDate);
            }
            if (endDate) {
                const endDateTime = new Date(endDate);
                endDateTime.setHours(23, 59, 59, 999);
                where.createdAt.lte = endDateTime;
            }
        }
        const [logs, total] = await Promise.all([
            this.prisma.systemLog.findMany({
                where,
                skip: (page - 1) * pageSize,
                take: pageSize,
                orderBy: { createdAt: 'desc' },
                include: {
                    admin: {
                        select: {
                            id: true,
                            username: true,
                            role: true
                        }
                    },
                    merchant: {
                        select: {
                            id: true,
                            username: true,
                            membershipLevel: true
                        }
                    },
                    user: {
                        select: {
                            id: true,
                            username: true,
                            membershipLevel: true
                        }
                    }
                }
            }),
            this.prisma.systemLog.count({ where })
        ]);
        // 格式化返回数据
        const formattedLogs = logs.map(log => ({
            id: log.id,
            userType: log.userType,
            userId: log.adminId || log.merchantId || log.userId,
            username: log.admin?.username || log.merchant?.username || log.user?.username || '未知用户',
            userRole: log.admin?.role || log.merchant?.membershipLevel || log.user?.membershipLevel || '',
            action: log.action,
            resource: log.resource,
            details: log.details,
            ip: log.ip || '',
            userAgent: log.userAgent || '',
            level: log.level || 'INFO',
            context: log.context || null,
            createdAt: log.createdAt
        }));
        return { logs: formattedLogs, total };
    }
    /**
     * 创建系统日志
     */
    async createSystemLog(data) {
        // 验证用户ID是否存在
        let validAdminId = null;
        let validMerchantId = null;
        let validUserId = null;
        if (data.adminId) {
            const admin = await this.prisma.admin.findUnique({
                where: { id: data.adminId }
            });
            if (admin) {
                validAdminId = data.adminId;
            }
        }
        if (data.merchantId) {
            const merchant = await this.prisma.merchant.findUnique({
                where: { id: data.merchantId }
            });
            if (merchant) {
                validMerchantId = data.merchantId;
            }
        }
        if (data.userId) {
            const user = await this.prisma.user.findUnique({
                where: { id: data.userId }
            });
            if (user) {
                validUserId = data.userId;
            }
        }
        return await this.prisma.systemLog.create({
            data: {
                adminId: validAdminId,
                merchantId: validMerchantId,
                userId: validUserId,
                userType: data.userType,
                action: data.action,
                resource: data.resource,
                details: data.details,
                ip: data.ip,
                userAgent: data.userAgent,
                level: data.level || 'INFO',
                context: data.context
            }
        });
    }
    /**
     * 获取系统日志统计信息
     */
    async getSystemLogStats() {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        // 使用原始查询来获取统计数据，避免类型错误
        const [totalLogs, todayLogs] = await Promise.all([
            this.prisma.systemLog.count(),
            this.prisma.systemLog.count({
                where: {
                    createdAt: {
                        gte: today
                    }
                }
            })
        ]);
        // 使用原始查询获取按级别统计的数据
        const levelStats = await this.prisma.$queryRaw `
      SELECT level, COUNT(*) as count FROM system_logs GROUP BY level
    `;
        const errorLogs = Number(levelStats.find(s => s.level === 'ERROR')?.count || 0);
        const warnLogs = Number(levelStats.find(s => s.level === 'WARN')?.count || 0);
        const infoLogs = Number(levelStats.find(s => s.level === 'INFO')?.count || 0);
        const debugLogs = Number(levelStats.find(s => s.level === 'DEBUG')?.count || 0);
        // 获取最近的错误日志
        const recentErrorLogs = await this.prisma.$queryRaw `
      SELECT id, userType, action, resource, details, createdAt, adminId, merchantId, userId
      FROM system_logs 
      WHERE level = 'ERROR' 
      ORDER BY createdAt DESC 
      LIMIT 10
    `;
        // 获取用户名信息
        const enrichedErrorLogs = await Promise.all(recentErrorLogs.map(async (log) => {
            let username = '未知用户';
            if (log.adminId) {
                const admin = await this.prisma.admin.findUnique({
                    where: { id: log.adminId },
                    select: { username: true }
                });
                username = admin?.username || '未知管理员';
            }
            else if (log.merchantId) {
                const merchant = await this.prisma.merchant.findUnique({
                    where: { id: log.merchantId },
                    select: { username: true }
                });
                username = merchant?.username || '未知商家';
            }
            else if (log.userId) {
                const user = await this.prisma.user.findUnique({
                    where: { id: log.userId },
                    select: { username: true }
                });
                username = user?.username || '未知用户';
            }
            return {
                id: log.id,
                userType: log.userType,
                username,
                action: log.action,
                resource: log.resource,
                details: log.details,
                createdAt: log.createdAt
            };
        }));
        return {
            totalLogs,
            todayLogs,
            errorLogs,
            warnLogs,
            infoLogs,
            debugLogs,
            recentErrorLogs: enrichedErrorLogs
        };
    }
    /**
     * 获取操作统计
     */
    async getActionStats(days = 7) {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        const [actionCounts, resourceCounts, userTypeCounts] = await Promise.all([
            this.prisma.systemLog.groupBy({
                by: ['action'],
                where: {
                    createdAt: {
                        gte: startDate
                    }
                },
                _count: {
                    action: true
                },
                orderBy: {
                    _count: {
                        action: 'desc'
                    }
                },
                take: 10
            }),
            this.prisma.systemLog.groupBy({
                by: ['resource'],
                where: {
                    createdAt: {
                        gte: startDate
                    }
                },
                _count: {
                    resource: true
                },
                orderBy: {
                    _count: {
                        resource: 'desc'
                    }
                },
                take: 10
            }),
            this.prisma.systemLog.groupBy({
                by: ['userType'],
                where: {
                    createdAt: {
                        gte: startDate
                    }
                },
                _count: {
                    userType: true
                },
                orderBy: {
                    _count: {
                        userType: 'desc'
                    }
                }
            })
        ]);
        return {
            actionCounts: actionCounts.map(item => ({
                action: item.action,
                count: item._count.action
            })),
            resourceCounts: resourceCounts.map(item => ({
                resource: item.resource,
                count: item._count.resource
            })),
            userTypeCounts: userTypeCounts.map(item => ({
                userType: item.userType,
                count: item._count.userType
            }))
        };
    }
    /**
     * 清理过期日志
     */
    async cleanupOldLogs(days = 90) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);
        const result = await this.prisma.systemLog.deleteMany({
            where: {
                createdAt: {
                    lt: cutoffDate
                },
                NOT: {
                    level: 'ERROR' // 保留错误日志
                }
            }
        });
        return { deletedCount: result.count };
    }
    /**
     * 获取日志详情
     */
    async getSystemLogDetail(id) {
        const log = await this.prisma.systemLog.findUnique({
            where: { id },
            include: {
                admin: {
                    select: {
                        id: true,
                        username: true,
                        role: true,
                        email: true
                    }
                },
                merchant: {
                    select: {
                        id: true,
                        username: true,
                        membershipLevel: true,
                        email: true
                    }
                },
                user: {
                    select: {
                        id: true,
                        username: true,
                        membershipLevel: true,
                        email: true
                    }
                }
            }
        });
        if (!log) {
            throw new Error('日志记录不存在');
        }
        return {
            id: log.id,
            userType: log.userType,
            userId: log.adminId || log.merchantId || log.userId,
            userInfo: log.admin || log.merchant || log.user,
            action: log.action,
            resource: log.resource,
            details: log.details,
            ip: log.ip,
            userAgent: log.userAgent,
            level: log.level || 'INFO',
            context: log.context || null,
            createdAt: log.createdAt
        };
    }
}
exports.SystemLogService = SystemLogService;
//# sourceMappingURL=system-log.service.js.map