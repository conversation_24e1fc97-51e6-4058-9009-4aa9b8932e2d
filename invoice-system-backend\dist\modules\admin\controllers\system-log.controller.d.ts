/**
 * 系统日志控制器
 */
import { Request, Response } from 'express';
import { BaseAdminController } from './base-admin.controller';
export declare class SystemLogController extends BaseAdminController {
    /**
     * 获取系统日志列表
     */
    getSystemLogList(req: Request, res: Response): Promise<void>;
    /**
     * 获取系统日志详情
     */
    getSystemLogDetail(req: Request, res: Response): Promise<void>;
    /**
     * 获取系统日志统计信息
     */
    getSystemLogStats(req: Request, res: Response): Promise<void>;
    /**
     * 获取操作统计
     */
    getActionStats(req: Request, res: Response): Promise<void>;
    /**
     * 清理过期日志
     */
    cleanupOldLogs(req: Request, res: Response): Promise<void>;
    /**
     * 获取日志级别统计
     */
    getLogLevelStats(req: Request, res: Response): Promise<void>;
    /**
     * 获取用户操作排行
     */
    getUserActionRanking(req: Request, res: Response): Promise<void>;
    /**
     * 获取系统日志趋势数据
     */
    getLogTrends(req: Request, res: Response): Promise<void>;
}
//# sourceMappingURL=system-log.controller.d.ts.map