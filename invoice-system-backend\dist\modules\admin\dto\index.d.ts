/**
 * 管理员模块数据传输对象
 */
export interface GetUserListDto {
    page?: number;
    pageSize?: number;
    search?: string;
    status?: string;
}
export interface GetMerchantListDto {
    page?: number;
    pageSize?: number;
    search?: string;
    status?: string;
    membershipLevel?: string;
}
export interface GetCompanyListDto {
    page?: number;
    pageSize?: number;
    search?: string;
    status?: string;
    merchantId?: number;
}
export interface GetInvoiceListDto {
    page?: number;
    pageSize?: number;
    search?: string;
    status?: string;
    type?: string;
    merchantId?: number;
    companyId?: number;
}
export interface GetTaskListDto {
    page?: number;
    pageSize?: number;
    search?: string;
    status?: string;
    type?: string;
}
export interface DashboardStatsResponseDto {
    totalUsers: number;
    totalMerchants: number;
    totalCompanies: number;
    totalInvoices: number;
    todayInvoices: number;
    monthlyInvoices: number;
    totalAmount: number;
    monthlyAmount: number;
    userStats: Record<string, number>;
    merchantStats: Record<string, number>;
    invoiceStats: Record<string, number>;
    recentActivities: Array<{
        id: number;
        type: string;
        description: string;
        createdAt: Date;
    }>;
}
export interface UserListResponseDto {
    users: Array<{
        id: number;
        username: string;
        email?: string;
        phone?: string;
        status: string;
        createdAt: Date;
        lastLoginAt?: Date;
    }>;
    total: number;
}
export interface MerchantListResponseDto {
    merchants: Array<{
        id: number;
        username: string;
        email?: string;
        phone?: string;
        status: string;
        membershipLevel: string;
        membershipExpiry?: Date;
        companiesCount: number;
        invoiceStaffsCount: number;
        invoicesCount: number;
        totalAmount: number;
        createdAt: Date;
        lastLoginAt?: Date;
    }>;
    total: number;
}
//# sourceMappingURL=index.d.ts.map