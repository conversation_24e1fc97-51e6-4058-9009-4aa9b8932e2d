"use strict";
/**
 * 商家管理控制器
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MerchantAdminController = void 0;
const base_admin_controller_1 = require("./base-admin.controller");
const response_1 = require("../../../shared/utils/response");
const systemLog_decorator_1 = require("../../../shared/decorators/systemLog.decorator");
class MerchantAdminController extends base_admin_controller_1.BaseAdminController {
    async getMerchantList(req, res) {
        try {
            const { page = 1, limit = 10, search, status } = req.query;
            const result = await this.adminService.getMerchantList({
                page: Number(page),
                pageSize: Number(limit),
                search: search,
                status: status
            });
            res.json((0, response_1.successResponse)(result, '获取商家列表成功'));
        }
        catch (error) {
            console.error('获取商家列表失败:', error);
            res.status(500).json((0, response_1.errorResponse)('获取商家列表失败'));
        }
    }
    async getMerchantDetail(req, res) {
        try {
            const { id } = req.params;
            const merchant = await this.adminService.getMerchantDetail(Number(id));
            if (!merchant) {
                res.status(404).json((0, response_1.errorResponse)('商家不存在'));
                return;
            }
            res.json((0, response_1.successResponse)(merchant, '获取商家详情成功'));
        }
        catch (error) {
            console.error('获取商家详情失败:', error);
            res.status(500).json((0, response_1.errorResponse)('获取商家详情失败'));
        }
    }
    async createMerchant(req, res) {
        try {
            const merchantData = req.body;
            const merchant = await this.adminService.createMerchant(merchantData);
            res.status(201).json((0, response_1.successResponse)(merchant, '创建商家成功'));
        }
        catch (error) {
            console.error('创建商家失败:', error);
            res.status(500).json((0, response_1.errorResponse)('创建商家失败'));
        }
    }
    async updateMerchant(req, res) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            const merchant = await this.adminService.updateMerchant(Number(id), updateData);
            res.json((0, response_1.successResponse)(merchant, '更新商家成功'));
        }
        catch (error) {
            console.error('更新商家失败:', error);
            res.status(500).json((0, response_1.errorResponse)('更新商家失败'));
        }
    }
    async deleteMerchant(req, res) {
        try {
            const { id } = req.params;
            await this.adminService.deleteMerchant(Number(id));
            res.json((0, response_1.successResponse)(null, '删除商家成功'));
        }
        catch (error) {
            console.error('删除商家失败:', error);
            res.status(500).json((0, response_1.errorResponse)('删除商家失败'));
        }
    }
    async updateMerchantStatus(req, res) {
        try {
            const { id } = req.params;
            const { status } = req.body;
            const merchant = await this.adminService.updateMerchant(Number(id), { status });
            res.json((0, response_1.successResponse)(merchant, '更新商家状态成功'));
        }
        catch (error) {
            console.error('更新商家状态失败:', error);
            res.status(500).json((0, response_1.errorResponse)('更新商家状态失败'));
        }
    }
    async getMerchantStats(req, res) {
        try {
            const stats = await this.adminService.getMerchantStats();
            res.json((0, response_1.successResponse)(stats, '获取商家统计信息成功'));
        }
        catch (error) {
            console.error('获取商家统计信息失败:', error);
            res.status(500).json((0, response_1.errorResponse)('获取商家统计信息失败'));
        }
    }
}
exports.MerchantAdminController = MerchantAdminController;
__decorate([
    (0, systemLog_decorator_1.LogView)('商家管理'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], MerchantAdminController.prototype, "getMerchantList", null);
__decorate([
    (0, systemLog_decorator_1.LogView)('商家管理'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], MerchantAdminController.prototype, "getMerchantDetail", null);
__decorate([
    (0, systemLog_decorator_1.LogCreate)('商家管理'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], MerchantAdminController.prototype, "createMerchant", null);
__decorate([
    (0, systemLog_decorator_1.LogUpdate)('商家管理'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], MerchantAdminController.prototype, "updateMerchant", null);
__decorate([
    (0, systemLog_decorator_1.LogDelete)('商家管理'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], MerchantAdminController.prototype, "deleteMerchant", null);
__decorate([
    (0, systemLog_decorator_1.LogUpdate)('商家管理'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], MerchantAdminController.prototype, "updateMerchantStatus", null);
__decorate([
    (0, systemLog_decorator_1.LogView)('商家统计'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], MerchantAdminController.prototype, "getMerchantStats", null);
//# sourceMappingURL=merchant-admin.controller.js.map