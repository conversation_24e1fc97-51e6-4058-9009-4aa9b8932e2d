/**
 * 管理员服务模块
 */
import { PrismaClient } from '@prisma/client';
import { IAdminService } from '../interfaces/admin-service.interface';
import { DashboardStats, MerchantStats, UserStats, PaginationQuery, SystemLogListQuery } from '../../../shared/interfaces/types';
/**
 * 管理员服务模块 - 整合所有子服务
 */
export declare class AdminServiceModule implements IAdminService {
    private readonly prisma;
    private readonly dashboardService;
    private readonly userService;
    private readonly merchantService;
    private readonly companyService;
    private readonly invoiceService;
    private readonly taskService;
    private readonly systemLogService;
    private readonly exportService;
    private readonly invoiceStaffService;
    constructor(prisma: PrismaClient);
    getDashboardStats(): Promise<DashboardStats>;
    getUserList(query: PaginationQuery & {
        search?: string;
        status?: string;
    }): Promise<{
        users: any[];
        total: number;
    }>;
    getUserDetail(id: number): Promise<any>;
    createUser(data: any): Promise<any>;
    updateUser(id: number, data: any): Promise<any>;
    deleteUser(id: number): Promise<void>;
    getUserStats(): Promise<UserStats>;
    getMerchantList(query: PaginationQuery & {
        search?: string;
        status?: string;
        membershipLevel?: string;
    }): Promise<{
        merchants: any[];
        total: number;
    }>;
    getMerchantDetail(id: number): Promise<any>;
    createMerchant(data: any): Promise<any>;
    updateMerchant(id: number, data: any): Promise<any>;
    deleteMerchant(id: number): Promise<{
        success: boolean;
        message?: string;
        data?: any;
    }>;
    getMerchantStats(): Promise<MerchantStats>;
    getCompanyList(query: PaginationQuery & {
        search?: string;
        status?: string;
        merchantId?: number;
    }): Promise<{
        companies: any[];
        total: number;
    }>;
    getCompanyDetail(id: number): Promise<any>;
    createCompany(data: any): Promise<any>;
    updateCompany(id: number, data: any): Promise<any>;
    deleteCompany(id: number): Promise<void>;
    getCompanyStats(): Promise<any>;
    getInvoiceStaffList(query: PaginationQuery & {
        search?: string;
        status?: string;
        merchantId?: number;
        companyId?: number;
        boundStartDate?: string;
        boundEndDate?: string;
        createdStartDate?: string;
        createdEndDate?: string;
    }): Promise<{
        invoiceStaffs: any[];
        total: number;
    }>;
    getInvoiceStaffDetail(id: number): Promise<any>;
    createInvoiceStaff(data: any): Promise<any>;
    updateInvoiceStaff(id: number, data: any): Promise<any>;
    deleteInvoiceStaff(id: number): Promise<void>;
    getInvoiceStaffStats(): Promise<any>;
    getInvoiceList(query: PaginationQuery & {
        search?: string;
        status?: string;
        type?: string;
        merchantId?: number;
        companyId?: number;
    }): Promise<{
        invoices: any[];
        total: number;
    }>;
    getInvoiceDetail(id: number): Promise<any>;
    createInvoice(data: any): Promise<any>;
    updateInvoice(id: number, data: any): Promise<any>;
    deleteInvoice(id: number): Promise<any>;
    updateInvoiceStatus(id: number, status: string): Promise<any>;
    getTaskList(query: PaginationQuery & {
        search?: string;
        status?: string;
        type?: string;
    }): Promise<{
        tasks: any[];
        total: number;
    }>;
    getSystemLogList(query: SystemLogListQuery): Promise<{
        logs: any[];
        total: number;
    }>;
    createSystemLog(data: any): Promise<any>;
    getSystemLogDetail(id: number): Promise<any>;
    getSystemLogStats(): Promise<any>;
    getActionStats(days: number): Promise<any>;
    cleanupOldLogs(days: number): Promise<{
        deletedCount: number;
    }>;
    getLogLevelStats(days: number): Promise<any>;
    getUserActionRanking(days: number, userType?: string): Promise<any>;
    getLogTrends(days: number, level?: string): Promise<any>;
    exportData(type: string, format: string, filters: any): Promise<{
        data: any[];
        filename: string;
        headers: string[];
    }>;
}
//# sourceMappingURL=admin-service.module.d.ts.map