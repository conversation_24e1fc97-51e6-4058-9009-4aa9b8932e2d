"use strict";
/**
 * 公司管理控制器
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompanyAdminController = void 0;
const base_admin_controller_1 = require("./base-admin.controller");
const response_1 = require("../../../shared/utils/response");
const external_company_service_1 = require("../services/external-company.service");
const client_1 = require("@prisma/client");
const systemLog_decorator_1 = require("../../../shared/decorators/systemLog.decorator");
class CompanyAdminController extends base_admin_controller_1.BaseAdminController {
    constructor(adminService) {
        super(adminService);
        const prisma = new client_1.PrismaClient();
        this.externalCompanyService = new external_company_service_1.ExternalCompanyService(prisma);
    }
    /**
     * 获取公司列表
     */
    async getCompanyList(req, res) {
        try {
            const { page, pageSize, search, status } = this.getPaginationParams(req);
            const merchantId = req.query.merchantId ? parseInt(req.query.merchantId) : undefined;
            const result = await this.adminService.getCompanyList({
                page,
                pageSize,
                search,
                status,
                merchantId
            });
            res.json((0, response_1.successResponse)({
                companies: result.companies,
                pagination: {
                    page,
                    pageSize,
                    total: result.total,
                    totalPages: Math.ceil(result.total / pageSize)
                }
            }, '获取公司列表成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 获取公司详情
     */
    async getCompanyDetail(req, res) {
        try {
            const companyId = this.getIdParam(req);
            const company = await this.adminService.getCompanyDetail(companyId);
            res.json((0, response_1.successResponse)(company, '获取公司详情成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 创建公司
     */
    async createCompany(req, res) {
        try {
            const data = req.body;
            const result = await this.adminService.createCompany(data);
            res.json((0, response_1.successResponse)(result, '创建公司成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 更新公司
     */
    async updateCompany(req, res) {
        try {
            const companyId = this.getIdParam(req);
            const data = req.body;
            const result = await this.adminService.updateCompany(companyId, data);
            res.json((0, response_1.successResponse)(result, '更新公司成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 删除公司
     */
    async deleteCompany(req, res) {
        try {
            const companyId = this.getIdParam(req);
            await this.adminService.deleteCompany(companyId);
            res.json((0, response_1.successResponse)(null, '删除公司成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 同步删除公司
     */
    async syncDeleteCompany(req, res) {
        try {
            const { id } = req.params;
            const companyId = parseInt(id);
            if (isNaN(companyId)) {
                res.status(400).json({
                    success: false,
                    message: '无效的公司ID'
                });
                return;
            }
            const result = await this.externalCompanyService.syncDeleteCompany(companyId);
            if (result.success) {
                res.status(200).json({
                    success: true,
                    message: result.message
                });
            }
            else {
                res.status(200).json({
                    success: false,
                    message: result.message,
                    errorType: result.errorType
                });
            }
        }
        catch (error) {
            console.error('同步删除公司控制器错误:', error);
            res.status(500).json({
                success: false,
                message: '服务器内部错误'
            });
        }
    }
    /**
     * 获取公司统计信息
     */
    async getCompanyStats(req, res) {
        try {
            const stats = await this.adminService.getCompanyStats();
            res.json((0, response_1.successResponse)(stats, '获取公司统计信息成功'));
        }
        catch (error) {
            throw error;
        }
    }
}
exports.CompanyAdminController = CompanyAdminController;
__decorate([
    (0, systemLog_decorator_1.LogView)('COMPANY_LIST'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], CompanyAdminController.prototype, "getCompanyList", null);
__decorate([
    (0, systemLog_decorator_1.LogView)('COMPANY_DETAIL'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], CompanyAdminController.prototype, "getCompanyDetail", null);
__decorate([
    (0, systemLog_decorator_1.LogView)('COMPANY_CREATE'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], CompanyAdminController.prototype, "createCompany", null);
__decorate([
    (0, systemLog_decorator_1.LogUpdate)('COMPANY'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], CompanyAdminController.prototype, "updateCompany", null);
__decorate([
    (0, systemLog_decorator_1.LogDelete)('COMPANY'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], CompanyAdminController.prototype, "deleteCompany", null);
//# sourceMappingURL=company-admin.controller.js.map