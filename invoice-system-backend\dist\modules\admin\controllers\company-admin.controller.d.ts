/**
 * 公司管理控制器
 */
import { Request, Response } from 'express';
import { BaseAdminController } from './base-admin.controller';
export declare class CompanyAdminController extends BaseAdminController {
    private externalCompanyService;
    constructor(adminService: any);
    /**
     * 获取公司列表
     */
    getCompanyList(req: Request, res: Response): Promise<void>;
    /**
     * 获取公司详情
     */
    getCompanyDetail(req: Request, res: Response): Promise<void>;
    /**
     * 创建公司
     */
    createCompany(req: Request, res: Response): Promise<void>;
    /**
     * 更新公司
     */
    updateCompany(req: Request, res: Response): Promise<void>;
    /**
     * 删除公司
     */
    deleteCompany(req: Request, res: Response): Promise<void>;
    /**
     * 同步删除公司
     */
    syncDeleteCompany(req: Request, res: Response): Promise<void>;
    /**
     * 获取公司统计信息
     */
    getCompanyStats(req: Request, res: Response): Promise<void>;
}
//# sourceMappingURL=company-admin.controller.d.ts.map