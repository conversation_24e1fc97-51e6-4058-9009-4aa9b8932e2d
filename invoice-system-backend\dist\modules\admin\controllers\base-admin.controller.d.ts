/**
 * 管理员基础控制器
 */
import { Request, Response } from 'express';
import { IAdminService } from '../interfaces/admin-service.interface';
import { AuthenticatedRequest } from '../../../shared/interfaces/types';
export declare abstract class BaseAdminController {
    protected readonly adminService: IAdminService;
    constructor(adminService: IAdminService);
    /**
     * 获取管理员仪表板统计数据
     */
    getDashboardStats(req: AuthenticatedRequest, res: Response): Promise<void>;
    /**
     * 处理分页参数
     */
    protected getPaginationParams(req: Request): {
        page: number;
        pageSize: number;
        search: string;
        status: string;
    };
    /**
     * 处理ID参数
     */
    protected getIdParam(req: Request): number;
}
//# sourceMappingURL=base-admin.controller.d.ts.map