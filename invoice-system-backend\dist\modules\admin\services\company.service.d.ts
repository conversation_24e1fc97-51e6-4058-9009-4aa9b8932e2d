/**
 * 公司服务
 */
import { BaseService } from './base.service';
export declare class CompanyService extends BaseService {
    /**
     * 获取公司列表
     */
    getCompanies(params?: any): Promise<{
        companies: any[];
        total: number;
    }>;
    /**
     * 根据ID获取公司
     */
    getCompanyById(id: number): Promise<any>;
    /**
     * 创建公司
     */
    createCompany(data: any): Promise<any>;
    /**
     * 更新公司
     */
    updateCompany(id: number, data: any): Promise<any>;
    /**
     * 删除公司
     */
    deleteCompany(id: number): Promise<any>;
    /**
     * 获取公司统计信息
     */
    getCompanyStats(): Promise<any>;
}
//# sourceMappingURL=company.service.d.ts.map