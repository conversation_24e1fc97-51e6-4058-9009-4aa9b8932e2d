/**
 * Key-Value模式系统配置服务
 * 支持灵活的配置管理
 */
import { PrismaClient } from '@prisma/client';
export interface SystemConfigItem {
    key: string;
    value: string;
    type: 'string' | 'json' | 'number' | 'boolean';
    category: string;
    description?: string;
}
export interface SystemConfigData {
    apiUrl: string;
    aid: string;
    appSecret: string;
    callbackUrl: string;
    regionMappings: Record<string, string>;
}
export declare class KeyValueSystemConfigService {
    private prisma;
    constructor(prisma: PrismaClient);
    /**
     * 获取单个配置项
     */
    getConfig(key: string): Promise<any>;
    /**
     * 设置单个配置项
     */
    setConfig(key: string, value: any, type?: SystemConfigItem['type'], category?: string, description?: string): Promise<void>;
    /**
     * 获取分类下的所有配置
     */
    getConfigsByCategory(category: string): Promise<Record<string, any>>;
    /**
     * 获取完整的系统配置
     */
    getSystemConfig(): Promise<SystemConfigData>;
    /**
     * 保存完整的系统配置
     */
    saveSystemConfig(data: SystemConfigData): Promise<SystemConfigData>;
    /**
     * 获取地区编号映射
     */
    getRegionMappings(): Promise<Record<string, string>>;
    /**
     * 根据地区名称获取编号
     */
    getRegionCode(regionName: string): Promise<string | null>;
    /**
     * 获取API配置
     */
    getApiConfig(): Promise<{
        apiUrl: string;
        aid: string;
        appSecret: string;
        callbackUrl: string;
    } | null>;
    /**
     * 获取所有配置项（用于管理界面）
     */
    getAllConfigs(): Promise<SystemConfigItem[]>;
    /**
     * 批量设置配置项
     */
    setConfigs(configs: Omit<SystemConfigItem, 'id'>[]): Promise<void>;
    /**
     * 删除配置项
     */
    deleteConfig(key: string): Promise<void>;
    /**
     * 解析配置值
     */
    private parseConfigValue;
}
//# sourceMappingURL=key-value-system-config.service.d.ts.map