"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvoiceService = void 0;
/**
 * 发票服务
 */
const base_service_1 = require("./base.service");
const errorHandler_1 = require("../../../shared/middleware/errorHandler");
class InvoiceService extends base_service_1.BaseService {
    /**
     * 获取发票列表
     */
    async getInvoiceList(query) {
        const { page = 1, pageSize = 10, search, status, type, merchantId, companyId } = query;
        // 构建查询条件
        const where = {};
        if (search) {
            where.OR = [
                { invoiceNumber: { contains: search } },
                { invoiceCode: { contains: search } },
                { buyerName: { contains: search } }
            ];
        }
        if (status && status !== 'all') {
            where.status = status;
        }
        if (type && type !== 'all') {
            where.invoiceType = type;
        }
        if (merchantId) {
            where.merchantId = merchantId;
        }
        if (companyId) {
            where.companyId = companyId;
        }
        const [invoices, total] = await Promise.all([
            this.prisma.invoice.findMany({
                where,
                skip: (page - 1) * pageSize,
                take: pageSize,
                orderBy: { createdAt: 'desc' },
                include: {
                    merchant: {
                        select: {
                            id: true,
                            username: true
                        }
                    },
                    company: {
                        select: {
                            id: true,
                            name: true
                        }
                    },
                    user: {
                        select: {
                            id: true,
                            username: true
                        }
                    }
                }
            }),
            this.prisma.invoice.count({ where })
        ]);
        // 格式化返回数据
        const formattedInvoices = invoices.map(invoice => {
            return {
                id: invoice.id,
                invoiceNumber: invoice.invoiceNumber || '',
                invoiceCode: invoice.invoiceCode || '',
                invoiceType: invoice.invoiceType || 'NORMAL',
                status: invoice.status,
                amount: Number(invoice.amount),
                taxAmount: Number(invoice.taxAmount),
                totalAmount: Number(invoice.totalAmount),
                buyerName: invoice.buyerName,
                buyerTaxNumber: invoice.buyerTaxNumber || '',
                buyerAddress: invoice.buyerAddress || '',
                buyerPhone: invoice.buyerPhone || '',
                buyerBankName: invoice.buyerBankName || '',
                buyerBankAccount: invoice.buyerBankAccount || '',
                sellerName: invoice.sellerName || '',
                sellerTaxNumber: invoice.sellerTaxNumber || '',
                sellerAddress: invoice.sellerAddress || '',
                sellerPhone: invoice.sellerPhone || '',
                sellerBankName: invoice.sellerBankName || '',
                sellerBankAccount: invoice.sellerBankAccount || '',
                remark: invoice.remark || '',
                merchantId: invoice.merchantId,
                merchantName: invoice.merchant?.username || '',
                companyId: invoice.companyId,
                companyName: invoice.company?.name || '',
                userId: invoice.userId,
                userName: invoice.user?.username || '',
                issueDate: invoice.issueDate?.toISOString().split('T')[0] || null,
                pdfUrl: invoice.pdfUrl || '',
                createdAt: invoice.createdAt,
                updatedAt: invoice.updatedAt
            };
        });
        return { invoices: formattedInvoices, total };
    }
    /**
     * 获取发票详情
     */
    async getInvoiceDetail(id) {
        if (isNaN(id)) {
            throw new errorHandler_1.AppError('无效的发票ID', 400);
        }
        const invoice = await this.prisma.invoice.findUnique({
            where: { id },
            include: {
                merchant: {
                    select: {
                        id: true,
                        username: true,
                        email: true,
                        phone: true
                    }
                },
                company: {
                    select: {
                        id: true,
                        name: true,
                        taxNumber: true,
                        region: true
                    }
                },
                user: {
                    select: {
                        id: true,
                        username: true,
                        email: true,
                        phone: true
                    }
                },
                items: true,
                tasks: {
                    orderBy: { createdAt: 'desc' }
                }
            }
        });
        if (!invoice) {
            throw new errorHandler_1.AppError('发票不存在', 404);
        }
        // 格式化返回数据
        return {
            id: invoice.id,
            invoiceNumber: invoice.invoiceNumber || '',
            invoiceCode: invoice.invoiceCode || '',
            invoiceType: invoice.invoiceType || 'NORMAL',
            status: invoice.status,
            amount: Number(invoice.amount),
            taxAmount: Number(invoice.taxAmount),
            totalAmount: Number(invoice.totalAmount),
            buyerName: invoice.buyerName,
            buyerTaxNumber: invoice.buyerTaxNumber || '',
            buyerAddress: invoice.buyerAddress || '',
            buyerPhone: invoice.buyerPhone || '',
            buyerBankName: invoice.buyerBankName || '',
            buyerBankAccount: invoice.buyerBankAccount || '',
            sellerName: invoice.sellerName || '',
            sellerTaxNumber: invoice.sellerTaxNumber || '',
            sellerAddress: invoice.sellerAddress || '',
            sellerPhone: invoice.sellerPhone || '',
            sellerBankName: invoice.sellerBankName || '',
            sellerBankAccount: invoice.sellerBankAccount || '',
            remark: invoice.remark || '',
            merchant: invoice.merchant,
            company: invoice.company,
            user: invoice.user,
            items: invoice.items.map(item => ({
                id: item.id,
                name: item.name || item.itemName || '',
                specification: item.specification || '',
                unit: item.unit || '',
                quantity: Number(item.quantity || 0),
                unitPrice: Number(item.unitPrice || 0),
                amount: Number(item.amount || 0),
                taxRate: Number(item.taxRate || 0),
                taxAmount: Number(item.taxAmount || 0)
            })),
            tasks: invoice.tasks.map(task => ({
                id: task.id,
                taskId: task.externalTaskId || task.id.toString(),
                type: task.type || '',
                status: task.status || '',
                message: task.message || '',
                createdAt: task.createdAt
            })),
            pdfUrl: invoice.pdfUrl || '',
            issueDate: invoice.issueDate?.toISOString().split('T')[0] || null,
            createdAt: invoice.createdAt,
            updatedAt: invoice.updatedAt
        };
    }
    /**
     * 创建发票
     */
    async createInvoice(data) {
        // 获取默认的用户、商家、公司和开票员
        const [user, merchant, company, staff] = await Promise.all([
            this.prisma.user.findFirst(),
            this.prisma.merchant.findFirst(),
            this.prisma.company.findFirst(),
            this.prisma.invoiceStaff.findFirst()
        ]);
        if (!user || !merchant || !company || !staff) {
            throw new errorHandler_1.AppError('缺少必要的基础数据，无法创建发票', 400);
        }
        // 生成自定义发票号
        const invoiceCount = await this.prisma.invoice.count();
        const customInvoiceNumber = `INV-${new Date().getFullYear()}-${String(invoiceCount + 1).padStart(6, '0')}`;
        const invoice = await this.prisma.invoice.create({
            data: {
                userId: user.id,
                merchantId: merchant.id,
                companyId: company.id,
                staffId: staff.id,
                customInvoiceNumber,
                invoiceType: data.invoiceType || 'NORMAL',
                isTaxIncluded: true,
                buyerIdentityType: 'ENTERPRISE',
                buyerName: data.buyerName,
                buyerTaxNumber: data.buyerTaxNumber,
                buyerEmail: data.buyerEmail,
                reviewer: '系统管理员',
                remark: data.remark,
                status: 'PENDING',
                amount: data.amount || 0,
                taxAmount: data.taxAmount || 0,
                totalAmount: data.totalAmount || 0,
            }
        });
        // 记录创建日志
        await this.prisma.systemLog.create({
            data: {
                action: 'CREATE',
                resource: 'INVOICE',
                details: `创建发票: ${customInvoiceNumber}`,
                adminId: 1,
                userType: 'ADMIN'
            }
        });
        return {
            id: invoice.id,
            status: 'success',
            message: '发票创建成功'
        };
    }
    /**
     * 更新发票
     */
    async updateInvoice(id, data) {
        if (isNaN(id)) {
            throw new errorHandler_1.AppError('无效的发票ID', 400);
        }
        const invoice = await this.prisma.invoice.findUnique({
            where: { id }
        });
        if (!invoice) {
            throw new errorHandler_1.AppError('发票不存在', 404);
        }
        const updatedInvoice = await this.prisma.invoice.update({
            where: { id },
            data: {
                buyerName: data.buyerName || invoice.buyerName,
                buyerTaxNumber: data.buyerTaxNumber || invoice.buyerTaxNumber,
                buyerEmail: data.buyerEmail || invoice.buyerEmail,
                remark: data.remark || invoice.remark,
                amount: data.amount !== undefined ? data.amount : invoice.amount,
                taxAmount: data.taxAmount !== undefined ? data.taxAmount : invoice.taxAmount,
                totalAmount: data.totalAmount !== undefined ? data.totalAmount : invoice.totalAmount,
                updatedAt: new Date()
            }
        });
        // 记录更新日志
        await this.prisma.systemLog.create({
            data: {
                action: 'UPDATE',
                resource: 'INVOICE',
                details: `更新发票: ${invoice.customInvoiceNumber}`,
                adminId: 1,
                userType: 'ADMIN'
            }
        });
        return {
            id: updatedInvoice.id,
            status: 'success',
            message: '发票更新成功'
        };
    }
    /**
     * 删除发票
     */
    async deleteInvoice(id) {
        if (isNaN(id)) {
            throw new errorHandler_1.AppError('无效的发票ID', 400);
        }
        const invoice = await this.prisma.invoice.findUnique({
            where: { id }
        });
        if (!invoice) {
            throw new errorHandler_1.AppError('发票不存在', 404);
        }
        // 删除相关的发票项目
        await this.prisma.invoiceItem.deleteMany({
            where: { invoiceId: id }
        });
        // 删除发票
        await this.prisma.invoice.delete({
            where: { id }
        });
        // 记录删除日志
        await this.prisma.systemLog.create({
            data: {
                action: 'DELETE',
                resource: 'INVOICE',
                details: `删除发票: ${invoice.customInvoiceNumber}`,
                adminId: 1,
                userType: 'ADMIN'
            }
        });
        return {
            id,
            status: 'success',
            message: '发票删除成功'
        };
    }
    /**
     * 更新发票状态
     */
    async updateInvoiceStatus(id, status) {
        if (isNaN(id)) {
            throw new errorHandler_1.AppError('无效的发票ID', 400);
        }
        const validStatuses = ['DRAFT', 'PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED'];
        if (!validStatuses.includes(status)) {
            throw new errorHandler_1.AppError('无效的发票状态', 400);
        }
        const invoice = await this.prisma.invoice.findUnique({
            where: { id }
        });
        if (!invoice) {
            throw new errorHandler_1.AppError('发票不存在', 404);
        }
        const updatedInvoice = await this.prisma.invoice.update({
            where: { id },
            data: { status: status }
        });
        // 记录状态变更日志
        await this.prisma.systemLog.create({
            data: {
                action: 'UPDATE',
                resource: 'INVOICE',
                details: `发票状态从 ${invoice.status} 更新为 ${status}`,
                adminId: 1,
                userType: 'ADMIN'
            }
        });
        return {
            id: updatedInvoice.id,
            status: updatedInvoice.status,
            message: '发票状态更新成功'
        };
    }
}
exports.InvoiceService = InvoiceService;
//# sourceMappingURL=invoice.service.js.map