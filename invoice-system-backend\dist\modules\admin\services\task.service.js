"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskService = void 0;
/**
 * 任务服务
 */
const base_service_1 = require("./base.service");
class TaskService extends base_service_1.BaseService {
    /**
     * 获取任务列表
     */
    async getTaskList(query) {
        const { page = 1, pageSize = 10, search, status, type } = query;
        // 构建查询条件
        const where = {};
        if (search) {
            where.OR = [{ taskId: { contains: search } }, { description: { contains: search } }];
        }
        if (status && status !== 'all') {
            where.status = status;
        }
        if (type && type !== 'all') {
            where.type = type;
        }
        const [tasks, total] = await Promise.all([
            this.prisma.task.findMany({
                where,
                skip: (page - 1) * pageSize,
                take: pageSize,
                orderBy: { createdAt: 'desc' },
                include: {
                    invoice: {
                        select: {
                            id: true,
                            invoiceNumber: true,
                            buyerName: true
                        }
                    }
                }
            }),
            this.prisma.task.count({ where })
        ]);
        // 格式化返回数据
        const formattedTasks = tasks.map(task => ({
            id: task.id,
            taskId: task.externalTaskId || task.id.toString(),
            type: task.type,
            status: task.status,
            description: task.message || '',
            result: task.responseData ? JSON.stringify(task.responseData) : '',
            invoiceId: task.invoiceId,
            invoiceNumber: task.invoice?.invoiceNumber || '',
            buyerName: task.invoice?.buyerName || '',
            createdAt: task.createdAt,
            updatedAt: task.updatedAt
        }));
        return { tasks: formattedTasks, total };
    }
}
exports.TaskService = TaskService;
//# sourceMappingURL=task.service.js.map