/**
 * 发票员工管理控制器
 */
import { Request, Response } from 'express';
import { BaseAdminController } from './base-admin.controller';
export declare class InvoiceStaffAdminController extends BaseAdminController {
    private externalDrawerService;
    constructor(adminService: any);
    /**
     * 获取发票员工列表
     */
    getInvoiceStaffList(req: Request, res: Response): Promise<void>;
    /**
     * 获取发票员工详情
     */
    getInvoiceStaffDetail(req: Request, res: Response): Promise<void>;
    /**
     * 创建发票员工
     */
    createInvoiceStaff(req: Request, res: Response): Promise<void>;
    /**
     * 更新发票员工
     */
    updateInvoiceStaff(req: Request, res: Response): Promise<void>;
    /**
     * 删除发票员工
     */
    deleteInvoiceStaff(req: Request, res: Response): Promise<void>;
    /**
     * 获取发票员工统计信息
     */
    getInvoiceStaffStats(req: Request, res: Response): Promise<void>;
    /**
     * 绑定开票员
     */
    bindInvoiceStaff(req: Request, res: Response): Promise<void>;
    /**
     * 创建开票员
     */
    createDrawer(req: Request, res: Response): Promise<void>;
}
//# sourceMappingURL=invoice-staff-admin.controller.d.ts.map