"use strict";
/**
 * 管理员控制器模块导出
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminController = exports.SystemAdminController = exports.InvoiceAdminController = exports.InvoiceStaffAdminController = exports.CompanyAdminController = exports.MerchantAdminController = exports.UserAdminController = exports.BaseAdminController = void 0;
var base_admin_controller_1 = require("./base-admin.controller");
Object.defineProperty(exports, "BaseAdminController", { enumerable: true, get: function () { return base_admin_controller_1.BaseAdminController; } });
var user_admin_controller_1 = require("./user-admin.controller");
Object.defineProperty(exports, "UserAdminController", { enumerable: true, get: function () { return user_admin_controller_1.UserAdminController; } });
var merchant_admin_controller_1 = require("./merchant-admin.controller");
Object.defineProperty(exports, "MerchantAdminController", { enumerable: true, get: function () { return merchant_admin_controller_1.MerchantAdminController; } });
var company_admin_controller_1 = require("./company-admin.controller");
Object.defineProperty(exports, "CompanyAdminController", { enumerable: true, get: function () { return company_admin_controller_1.CompanyAdminController; } });
var invoice_staff_admin_controller_1 = require("./invoice-staff-admin.controller");
Object.defineProperty(exports, "InvoiceStaffAdminController", { enumerable: true, get: function () { return invoice_staff_admin_controller_1.InvoiceStaffAdminController; } });
var invoice_admin_controller_1 = require("./invoice-admin.controller");
Object.defineProperty(exports, "InvoiceAdminController", { enumerable: true, get: function () { return invoice_admin_controller_1.InvoiceAdminController; } });
var system_admin_controller_1 = require("./system-admin.controller");
Object.defineProperty(exports, "SystemAdminController", { enumerable: true, get: function () { return system_admin_controller_1.SystemAdminController; } });
var admin_controller_1 = require("./admin.controller");
Object.defineProperty(exports, "AdminController", { enumerable: true, get: function () { return admin_controller_1.AdminController; } });
//# sourceMappingURL=index.js.map