/**
 * 发票管理控制器
 */
import { Request, Response } from 'express';
import { BaseAdminController } from './base-admin.controller';
export declare class InvoiceAdminController extends BaseAdminController {
    /**
     * 获取发票列表
     */
    getInvoiceList(req: Request, res: Response): Promise<void>;
    /**
     * 获取发票详情
     */
    getInvoiceDetail(req: Request, res: Response): Promise<void>;
    /**
     * 更新发票状态
     */
    updateInvoiceStatus(req: Request, res: Response): Promise<void>;
    /**
     * 创建发票
     */
    createInvoice(req: Request, res: Response): Promise<void>;
    /**
     * 更新发票
     */
    updateInvoice(req: Request, res: Response): Promise<void>;
    /**
     * 删除发票
     */
    deleteInvoice(req: Request, res: Response): Promise<void>;
    /**
     * 获取发票统计信息
     */
    getInvoiceStats(req: Request, res: Response): Promise<void>;
}
//# sourceMappingURL=invoice-admin.controller.d.ts.map