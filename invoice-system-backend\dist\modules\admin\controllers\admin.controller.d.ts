/**
 * 管理员主控制器 - 整合所有子控制器
 */
import { Request, Response } from 'express';
import { IAdminService } from '../interfaces/admin-service.interface';
import { AuthenticatedRequest } from '../../../shared/interfaces/types';
import { BaseAdminController } from './base-admin.controller';
export declare class AdminController extends BaseAdminController {
    private userController;
    private merchantController;
    private companyController;
    private invoiceStaffController;
    private invoiceController;
    private systemController;
    constructor(adminService: IAdminService);
    getDashboardStats(req: AuthenticatedRequest, res: Response): Promise<void>;
    getUserList(req: Request, res: Response): Promise<void>;
    getUserDetail(req: Request, res: Response): Promise<void>;
    createUser(req: Request, res: Response): Promise<void>;
    updateUser(req: Request, res: Response): Promise<void>;
    deleteUser(req: Request, res: Response): Promise<void>;
    getUserStats(req: Request, res: Response): Promise<void>;
    getMerchantList(req: Request, res: Response): Promise<void>;
    getMerchantDetail(req: Request, res: Response): Promise<void>;
    createMerchant(req: Request, res: Response): Promise<void>;
    updateMerchant(req: Request, res: Response): Promise<void>;
    deleteMerchant(req: Request, res: Response): Promise<void>;
    getMerchantStats(req: Request, res: Response): Promise<void>;
    getCompanyList(req: Request, res: Response): Promise<void>;
    getCompanyDetail(req: Request, res: Response): Promise<void>;
    createCompany(req: Request, res: Response): Promise<void>;
    updateCompany(req: Request, res: Response): Promise<void>;
    deleteCompany(req: Request, res: Response): Promise<void>;
    syncDeleteCompany(req: Request, res: Response): Promise<void>;
    getCompanyStats(req: Request, res: Response): Promise<void>;
    getInvoiceStaffList(req: Request, res: Response): Promise<void>;
    getInvoiceStaffDetail(req: Request, res: Response): Promise<void>;
    createInvoiceStaff(req: Request, res: Response): Promise<void>;
    updateInvoiceStaff(req: Request, res: Response): Promise<void>;
    deleteInvoiceStaff(req: Request, res: Response): Promise<void>;
    getInvoiceStaffStats(req: Request, res: Response): Promise<void>;
    bindInvoiceStaff(req: Request, res: Response): Promise<void>;
    createDrawer(req: Request, res: Response): Promise<void>;
    getInvoiceList(req: Request, res: Response): Promise<void>;
    getInvoiceDetail(req: Request, res: Response): Promise<void>;
    createInvoice(req: Request, res: Response): Promise<void>;
    updateInvoice(req: Request, res: Response): Promise<void>;
    deleteInvoice(req: Request, res: Response): Promise<void>;
    updateInvoiceStatus(req: Request, res: Response): Promise<void>;
    getInvoiceTitleList(req: Request, res: Response): Promise<void>;
    getInvoiceTitleDetail(req: Request, res: Response): Promise<void>;
    createInvoiceTitle(req: Request, res: Response): Promise<void>;
    updateInvoiceTitle(req: Request, res: Response): Promise<void>;
    deleteInvoiceTitle(req: Request, res: Response): Promise<void>;
    getTaskList(req: Request, res: Response): Promise<void>;
    getSystemLogList(req: Request, res: Response): Promise<void>;
    createSystemLog(req: Request, res: Response): Promise<void>;
    getSystemLogDetail(req: Request, res: Response): Promise<void>;
    getSystemLogStats(req: Request, res: Response): Promise<void>;
    getActionStats(req: Request, res: Response): Promise<void>;
    getLogLevelStats(req: Request, res: Response): Promise<void>;
    getUserActionRanking(req: Request, res: Response): Promise<void>;
    getLogTrends(req: Request, res: Response): Promise<void>;
    cleanupOldLogs(req: Request, res: Response): Promise<void>;
    exportData(req: Request, res: Response): Promise<void>;
}
//# sourceMappingURL=admin.controller.d.ts.map