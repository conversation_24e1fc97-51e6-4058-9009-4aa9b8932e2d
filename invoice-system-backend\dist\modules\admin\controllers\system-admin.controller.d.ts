/**
 * 系统管理控制器
 */
import { Request, Response } from 'express';
import { BaseAdminController } from './base-admin.controller';
export declare class SystemAdminController extends BaseAdminController {
    /**
     * 获取任务列表
     */
    getTaskList(req: Request, res: Response): Promise<void>;
    /**
     * 获取系统日志列表
     */
    getSystemLogList(req: Request, res: Response): Promise<void>;
    /**
     * 创建系统日志
     */
    createSystemLog(req: Request, res: Response): Promise<void>;
    /**
     * 获取系统日志详情
     */
    getSystemLogDetail(req: Request, res: Response): Promise<void>;
    /**
     * 获取系统日志统计信息
     */
    getSystemLogStats(req: Request, res: Response): Promise<void>;
    /**
     * 获取操作统计
     */
    getActionStats(req: Request, res: Response): Promise<void>;
    /**
     * 获取日志级别统计
     */
    getLogLevelStats(req: Request, res: Response): Promise<void>;
    /**
     * 获取用户操作排行
     */
    getUserActionRanking(req: Request, res: Response): Promise<void>;
    /**
     * 获取系统日志趋势数据
     */
    getLogTrends(req: Request, res: Response): Promise<void>;
    /**
     * 清理过期日志
     */
    cleanupOldLogs(req: Request, res: Response): Promise<void>;
    /**
     * 导出数据
     */
    exportData(req: Request, res: Response): Promise<void>;
}
//# sourceMappingURL=system-admin.controller.d.ts.map