/**
 * 系统配置控制器 - Key-Value模式
 * 合并了原有的两个控制器功能，提供完整的系统配置管理
 */
import { Request, Response } from 'express';
export declare class SystemConfigController {
    private keyValueSystemConfigService;
    constructor();
    /**
     * 获取系统配置
     */
    getSystemConfig(req: Request, res: Response): Promise<void>;
    /**
     * 保存系统配置
     */
    saveSystemConfig(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    /**
     * 设置单个配置项
     */
    setConfig(req: Request, res: Response): Promise<void>;
    /**
     * 更新单个配置项（兼容原有接口）
     */
    updateConfigByKey(req: Request, res: Response): Promise<void>;
    /**
     * 获取单个配置项
     */
    getConfigByKey(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    /**
     * 删除配置项
     */
    deleteConfig(req: Request, res: Response): Promise<void>;
    /**
     * 获取所有配置项（兼容方法）
     */
    getAllConfigs(req: Request, res: Response): Promise<void>;
    /**
     * 获取地区映射（兼容方法）
     */
    getRegionMappings(req: Request, res: Response): Promise<void>;
    /**
     * 获取API配置（兼容方法）
     */
    getApiConfig(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
}
//# sourceMappingURL=system-config.controller.d.ts.map