import { Request, Response } from 'express';
import { BaseAdminController } from './base-admin.controller';
export declare class InvoiceTitleAdminController extends BaseAdminController {
    /**
     * 获取发票抬头列表
     */
    getInvoiceTitles(req: Request, res: Response): Promise<void>;
    /**
     * 获取发票抬头详情
     */
    getInvoiceTitle(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    /**
     * 创建发票抬头
     */
    createInvoiceTitle(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    /**
     * 更新发票抬头
     */
    updateInvoiceTitle(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    /**
     * 删除发票抬头
     */
    deleteInvoiceTitle(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    /**
     * 批量删除发票抬头
     */
    batchDeleteInvoiceTitles(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    /**
     * 获取发票抬头统计信息
     */
    getInvoiceTitleStats(req: Request, res: Response): Promise<void>;
}
//# sourceMappingURL=invoice-title-admin.controller.d.ts.map