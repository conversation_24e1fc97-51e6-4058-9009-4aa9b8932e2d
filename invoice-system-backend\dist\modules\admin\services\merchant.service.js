"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MerchantService = void 0;
/**
 * 商家服务
 */
const base_service_1 = require("./base.service");
const errorHandler_1 = require("../../../shared/middleware/errorHandler");
class MerchantService extends base_service_1.BaseService {
    /**
     * 获取商家列表
     */
    async getMerchantList(query) {
        const { page = 1, pageSize = 10, search, status, membershipLevel } = query;
        // 构建查询条件
        const where = {};
        if (search) {
            where.OR = [{ username: { contains: search } }, { email: { contains: search } }, { phone: { contains: search } }];
        }
        if (status && status !== 'all') {
            where.status = status;
        }
        if (membershipLevel && membershipLevel !== 'all') {
            where.membershipLevel = membershipLevel;
        }
        const [merchants, total] = await Promise.all([
            this.prisma.merchant.findMany({
                where,
                skip: (page - 1) * pageSize,
                take: pageSize,
                orderBy: { registeredAt: 'desc' },
                select: {
                    id: true,
                    avatar: true,
                    username: true,
                    phone: true,
                    email: true,
                    status: true,
                    membershipLevel: true,
                    membershipExpiry: true,
                    companiesCount: true,
                    staffsCount: true,
                    totalInvoices: true,
                    totalAmount: true,
                    registeredAt: true,
                    lastLoginAt: true,
                    createdAt: true,
                    updatedAt: true,
                    _count: {
                        select: {
                            companies: true,
                            invoices: true
                        }
                    }
                }
            }),
            this.prisma.merchant.count({ where })
        ]);
        // 格式化返回数据
        const formattedMerchants = merchants.map(merchant => ({
            id: merchant.id,
            avatar: merchant.avatar || null, // 如果没有头像则返回null，前端处理默认头像
            username: merchant.username,
            email: merchant.email || '',
            phone: merchant.phone || '',
            status: merchant.status,
            membershipLevel: merchant.membershipLevel,
            membershipExpiry: merchant.membershipExpiry?.toISOString().split('T')[0] || null,
            companiesCount: merchant._count.companies,
            staffsCount: merchant.staffsCount,
            totalInvoices: merchant.totalInvoices,
            totalAmount: Number(merchant.totalAmount),
            registeredAt: merchant.registeredAt.toISOString().split('T')[0],
            lastLogin: merchant.lastLoginAt
                ? merchant.lastLoginAt.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                })
                : '从未登录',
            createdAt: merchant.createdAt,
            updatedAt: merchant.updatedAt
        }));
        return { merchants: formattedMerchants, total };
    }
    /**
     * 获取商家详情
     */
    async getMerchantDetail(id) {
        if (isNaN(id)) {
            throw new errorHandler_1.AppError('无效的商家ID', 400);
        }
        const [merchant, companiesData] = await Promise.all([
            this.prisma.merchant.findUnique({
                where: { id },
                select: {
                    id: true,
                    avatar: true,
                    username: true,
                    phone: true,
                    email: true,
                    status: true,
                    membershipLevel: true,
                    membershipExpiry: true,
                    companiesCount: true,
                    staffsCount: true,
                    totalInvoices: true,
                    totalAmount: true,
                    registeredAt: true,
                    lastLoginAt: true,
                    lastLoginIp: true,
                    createdAt: true,
                    updatedAt: true
                }
            }),
            this.prisma.company.findMany({
                where: { merchantId: id },
                select: {
                    id: true,
                    name: true,
                    status: true,
                    createdAt: true
                }
            })
        ]);
        if (!merchant) {
            throw new errorHandler_1.AppError('商家不存在', 404);
        }
        return {
            id: merchant.id,
            avatar: merchant.avatar || '/placeholder.svg?height=40&width=40',
            username: merchant.username,
            email: merchant.email || '',
            phone: merchant.phone || '',
            status: merchant.status,
            membershipLevel: merchant.membershipLevel,
            membershipExpiry: merchant.membershipExpiry?.toISOString().split('T')[0] || null,
            companiesCount: merchant.companiesCount,
            staffsCount: merchant.staffsCount,
            totalInvoices: merchant.totalInvoices,
            totalAmount: Number(merchant.totalAmount),
            registeredAt: merchant.registeredAt.toISOString().split('T')[0],
            lastLogin: merchant.lastLoginAt
                ? merchant.lastLoginAt.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                })
                : '从未登录',
            companies: companiesData,
            createdAt: merchant.createdAt,
            updatedAt: merchant.updatedAt
        };
    }
    /**
     * 创建商家
     */
    async createMerchant(data) {
        const { username, email, phone, password, membershipLevel, membershipExpiry } = data;
        // 检查用户名、邮箱或手机号是否已存在
        const existingMerchant = await this.prisma.merchant.findFirst({
            where: {
                OR: [
                    username ? { username } : {},
                    email ? { email } : {},
                    phone ? { phone } : {}
                ].filter(condition => Object.keys(condition).length > 0)
            }
        });
        if (existingMerchant) {
            throw new errorHandler_1.AppError('用户名、邮箱或手机号已存在', 400);
        }
        // 创建商家
        const merchant = await this.prisma.merchant.create({
            data: {
                username: username || `merchant_${Date.now()}`,
                email: email || null,
                phone: phone || null,
                password, // 注意：实际应用中需要加密密码
                membershipLevel: membershipLevel || '普通会员',
                membershipExpiry: membershipExpiry ? new Date(membershipExpiry) : null,
                status: 'ACTIVE',
                companiesCount: 0,
                staffsCount: 0,
                totalInvoices: 0,
                totalAmount: 0,
                registeredAt: new Date()
            }
        });
        return {
            id: merchant.id,
            username: merchant.username,
            email: merchant.email
        };
    }
    /**
     * 更新商家
     */
    async updateMerchant(id, data) {
        // 检查商家是否存在
        const existingMerchant = await this.prisma.merchant.findUnique({
            where: { id }
        });
        if (!existingMerchant) {
            throw new errorHandler_1.AppError('商家不存在', 404);
        }
        // 更新商家信息
        const updatedMerchant = await this.prisma.merchant.update({
            where: { id },
            data: {
                ...data,
                membershipExpiry: data.membershipExpiry ? new Date(data.membershipExpiry) : undefined,
                updatedAt: new Date()
            }
        });
        return {
            id: updatedMerchant.id,
            username: updatedMerchant.username,
            email: updatedMerchant.email
        };
    }
    /**
     * 删除商家
     */
    async deleteMerchant(id) {
        // 检查商家是否存在
        const existingMerchant = await this.prisma.merchant.findUnique({
            where: { id }
        });
        if (!existingMerchant) {
            throw new errorHandler_1.AppError('商家不存在', 404);
        }
        // 检查是否有关联的公司
        const companyCount = await this.prisma.company.count({
            where: { merchantId: id }
        });
        if (companyCount > 0) {
            return {
                success: false,
                message: '该商家下还有关联的公司，无法删除',
                data: { companyCount }
            };
        }
        // 删除商家
        await this.prisma.merchant.delete({
            where: { id }
        });
        return {
            success: true,
            message: '商家删除成功'
        };
    }
    /**
     * 获取商家统计信息
     */
    async getMerchantStats() {
        const { thisMonth } = this.getDateRanges();
        const [totalMerchants, verifiedMerchants, bannedMerchants, vipMerchants, newMerchantsThisMonth] = await Promise.all([
            // 总商家数
            this.prisma.merchant.count(),
            // 正常状态商家数
            this.prisma.merchant.count({
                where: { status: 'ACTIVE' }
            }),
            // 禁用商家数
            this.prisma.merchant.count({
                where: { status: 'BANNED' }
            }),
            // VIP商家数
            this.prisma.merchant.count({
                where: {
                    membershipLevel: {
                        in: ['黄金会员', '钻石会员']
                    }
                }
            }),
            // 本月新增商家数
            this.prisma.merchant.count({
                where: {
                    registeredAt: {
                        gte: thisMonth
                    }
                }
            })
        ]);
        return {
            totalMerchants,
            verifiedMerchants,
            bannedMerchants,
            vipMerchants,
            newMerchantsThisMonth
        };
    }
}
exports.MerchantService = MerchantService;
//# sourceMappingURL=merchant.service.js.map