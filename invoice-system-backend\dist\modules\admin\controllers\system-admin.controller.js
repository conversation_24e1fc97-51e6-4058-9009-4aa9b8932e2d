"use strict";
/**
 * 系统管理控制器
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemAdminController = void 0;
const XLSX = __importStar(require("xlsx"));
const base_admin_controller_1 = require("./base-admin.controller");
const response_1 = require("../../../shared/utils/response");
const errorHandler_1 = require("../../../shared/middleware/errorHandler");
class SystemAdminController extends base_admin_controller_1.BaseAdminController {
    /**
     * 获取任务列表
     */
    async getTaskList(req, res) {
        try {
            const { page, pageSize, search, status } = this.getPaginationParams(req);
            const type = req.query.type;
            const result = await this.adminService.getTaskList({
                page,
                pageSize,
                search,
                status,
                type
            });
            res.json((0, response_1.paginationResponse)(result.tasks, result.total, page, pageSize, '获取任务列表成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 获取系统日志列表
     */
    async getSystemLogList(req, res) {
        try {
            const { page, pageSize, search } = this.getPaginationParams(req);
            const userType = req.query.userType;
            const action = req.query.action;
            const resource = req.query.resource;
            const level = req.query.level;
            const startDate = req.query.startDate;
            const endDate = req.query.endDate;
            const result = await this.adminService.getSystemLogList({
                page,
                pageSize,
                search,
                userType: userType,
                action,
                resource,
                level,
                startDate,
                endDate
            });
            res.json((0, response_1.paginationResponse)(result.logs, result.total, page, pageSize, '获取系统日志列表成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 创建系统日志
     */
    async createSystemLog(req, res) {
        try {
            const logData = req.body;
            // 验证必需字段
            if (!logData.userType || !logData.action || !logData.resource) {
                throw new errorHandler_1.AppError('缺少必需字段：userType, action, resource', 400);
            }
            const log = await this.adminService.createSystemLog(logData);
            res.json((0, response_1.successResponse)(log, '创建系统日志成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 获取系统日志详情
     */
    async getSystemLogDetail(req, res) {
        try {
            const id = parseInt(req.params.id);
            if (isNaN(id)) {
                throw new errorHandler_1.AppError('无效的日志ID', 400);
            }
            const log = await this.adminService.getSystemLogDetail(id);
            res.json((0, response_1.successResponse)(log, '获取日志详情成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 获取系统日志统计信息
     */
    async getSystemLogStats(req, res) {
        try {
            const stats = await this.adminService.getSystemLogStats();
            res.json((0, response_1.successResponse)(stats, '获取日志统计成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 获取操作统计
     */
    async getActionStats(req, res) {
        try {
            const days = parseInt(req.query.days) || 7;
            const stats = await this.adminService.getActionStats(days);
            res.json((0, response_1.successResponse)(stats, '获取操作统计成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 获取日志级别统计
     */
    async getLogLevelStats(req, res) {
        try {
            const days = parseInt(req.query.days) || 7;
            const stats = await this.adminService.getLogLevelStats(days);
            res.json((0, response_1.successResponse)(stats, '获取日志级别统计成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 获取用户操作排行
     */
    async getUserActionRanking(req, res) {
        try {
            const days = parseInt(req.query.days) || 7;
            const userType = req.query.userType;
            const ranking = await this.adminService.getUserActionRanking(days, userType);
            res.json((0, response_1.successResponse)(ranking, '获取用户操作排行成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 获取系统日志趋势数据
     */
    async getLogTrends(req, res) {
        try {
            const days = parseInt(req.query.days) || 30;
            const level = req.query.level;
            const trends = await this.adminService.getLogTrends(days, level);
            res.json((0, response_1.successResponse)(trends, '获取日志趋势数据成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 清理过期日志
     */
    async cleanupOldLogs(req, res) {
        try {
            const days = parseInt(req.body.days) || 90;
            if (days < 30) {
                throw new errorHandler_1.AppError('保留天数不能少于30天', 400);
            }
            const result = await this.adminService.cleanupOldLogs(days);
            res.json((0, response_1.successResponse)(result, `成功清理 ${result.deletedCount} 条过期日志`));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 导出数据
     */
    async exportData(req, res) {
        try {
            const { type, format: exportFormat = 'excel', ...filters } = req.query;
            const result = await this.adminService.exportData(type, exportFormat, filters);
            const formatType = String(exportFormat);
            if (formatType === 'excel') {
                // 生成Excel文件
                const worksheet = XLSX.utils.json_to_sheet(result.data);
                const workbook = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(workbook, worksheet, '数据列表');
                const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
                res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
                res.setHeader('Content-Disposition', `attachment; filename=${result.filename}.xlsx`);
                res.send(buffer);
            }
            else if (formatType === 'csv') {
                // 生成CSV内容
                let csvContent = result.headers.join(',') + '\n';
                result.data.forEach((row) => {
                    const values = result.headers.map((header) => {
                        const value = row[header];
                        return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
                    });
                    csvContent += values.join(',') + '\n';
                });
                res.setHeader('Content-Type', 'text/csv; charset=utf-8');
                res.setHeader('Content-Disposition', `attachment; filename=${result.filename}.csv`);
                res.send('\uFEFF' + csvContent);
            }
            else {
                throw new errorHandler_1.AppError('不支持的导出格式，请使用 excel 或 csv', 400);
            }
        }
        catch (error) {
            throw error;
        }
    }
}
exports.SystemAdminController = SystemAdminController;
//# sourceMappingURL=system-admin.controller.js.map