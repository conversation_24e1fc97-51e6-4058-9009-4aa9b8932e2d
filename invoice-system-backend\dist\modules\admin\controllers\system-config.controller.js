"use strict";
/**
 * 系统配置控制器 - Key-Value模式
 * 合并了原有的两个控制器功能，提供完整的系统配置管理
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemConfigController = void 0;
const client_1 = require("@prisma/client");
const key_value_system_config_service_1 = require("../services/key-value-system-config.service");
const response_1 = require("../../../shared/utils/response");
const systemLog_decorator_1 = require("../../../shared/decorators/systemLog.decorator");
const prisma = new client_1.PrismaClient();
const systemConfigService = new key_value_system_config_service_1.KeyValueSystemConfigService(prisma);
class SystemConfigController {
    constructor() {
        this.keyValueSystemConfigService = systemConfigService;
    }
    /**
     * 获取系统配置
     */
    async getSystemConfig(req, res) {
        try {
            const config = await this.keyValueSystemConfigService.getSystemConfig();
            res.json((0, response_1.successResponse)(config, '获取系统配置成功'));
        }
        catch (error) {
            console.error('获取系统配置失败:', error);
            res.status(500).json((0, response_1.errorResponse)('获取系统配置失败'));
        }
    }
    /**
     * 保存系统配置
     */
    async saveSystemConfig(req, res) {
        try {
            const { apiUrl, aid, appSecret, callbackUrl, regionMappings } = req.body;
            // 验证必填字段
            if (!apiUrl || !aid || !appSecret) {
                return res.status(400).json((0, response_1.errorResponse)('API地址、应用ID和应用密钥为必填项'));
            }
            // 验证回调地址格式（如果提供了的话）
            if (callbackUrl && !callbackUrl.startsWith('http')) {
                return res.status(400).json((0, response_1.errorResponse)('回调地址必须是有效的HTTP/HTTPS地址'));
            }
            // 验证地区映射格式
            if (regionMappings && typeof regionMappings !== 'object') {
                return res.status(400).json((0, response_1.errorResponse)('地区映射格式不正确'));
            }
            const configData = {
                apiUrl,
                aid,
                appSecret,
                callbackUrl: callbackUrl || '',
                regionMappings: regionMappings || {}
            };
            const savedConfig = await this.keyValueSystemConfigService.saveSystemConfig(configData);
            res.json((0, response_1.successResponse)(savedConfig, '保存系统配置成功'));
        }
        catch (error) {
            console.error('保存系统配置失败:', error);
            res.status(500).json((0, response_1.errorResponse)('保存系统配置失败'));
        }
    }
    /**
     * 设置单个配置项
     */
    async setConfig(req, res) {
        try {
            const { key } = req.params;
            const { value, type = 'string', category = 'general', description } = req.body;
            await this.keyValueSystemConfigService.setConfig(key, value, type, category, description);
            res.json((0, response_1.successResponse)(null, '设置配置成功'));
        }
        catch (error) {
            console.error('设置配置失败:', error);
            res.status(500).json((0, response_1.errorResponse)('设置配置失败'));
        }
    }
    /**
     * 更新单个配置项（兼容原有接口）
     */
    async updateConfigByKey(req, res) {
        try {
            const { key } = req.params;
            const { value, type = 'string', category = 'general', description } = req.body;
            await this.keyValueSystemConfigService.setConfig(key, value, type, category, description);
            // 获取更新后的配置项
            const updatedValue = await this.keyValueSystemConfigService.getConfig(key);
            const config = {
                key,
                value: updatedValue,
                type,
                category,
                description
            };
            res.json((0, response_1.successResponse)(config, '配置项更新成功'));
        }
        catch (error) {
            console.error('更新配置项失败:', error);
            res.status(500).json((0, response_1.errorResponse)('更新配置项失败'));
        }
    }
    /**
     * 获取单个配置项
     */
    async getConfigByKey(req, res) {
        try {
            const { key } = req.params;
            const value = await this.keyValueSystemConfigService.getConfig(key);
            if (value === null) {
                return res.status(404).json((0, response_1.errorResponse)('配置项不存在'));
            }
            res.json((0, response_1.successResponse)({ key, value }, '获取配置成功'));
        }
        catch (error) {
            console.error('获取配置失败:', error);
            res.status(500).json((0, response_1.errorResponse)('获取配置失败'));
        }
    }
    /**
     * 删除配置项
     */
    async deleteConfig(req, res) {
        try {
            const { key } = req.params;
            await this.keyValueSystemConfigService.deleteConfig(key);
            res.json((0, response_1.successResponse)(null, '删除配置成功'));
        }
        catch (error) {
            console.error('删除配置失败:', error);
            res.status(500).json((0, response_1.errorResponse)('删除配置失败'));
        }
    }
    /**
     * 获取所有配置项（兼容方法）
     */
    async getAllConfigs(req, res) {
        try {
            const configs = await this.keyValueSystemConfigService.getAllConfigs();
            res.json((0, response_1.successResponse)(configs, '获取所有配置成功'));
        }
        catch (error) {
            console.error('获取所有配置失败:', error);
            res.status(500).json((0, response_1.errorResponse)('获取所有配置失败'));
        }
    }
    /**
     * 获取地区映射（兼容方法）
     */
    async getRegionMappings(req, res) {
        try {
            const mappings = await this.keyValueSystemConfigService.getRegionMappings();
            res.json((0, response_1.successResponse)(mappings, '获取地区映射成功'));
        }
        catch (error) {
            console.error('获取地区映射失败:', error);
            res.status(500).json((0, response_1.errorResponse)('获取地区映射失败'));
        }
    }
    /**
     * 获取API配置（兼容方法）
     */
    async getApiConfig(req, res) {
        try {
            const apiConfig = await this.keyValueSystemConfigService.getApiConfig();
            if (!apiConfig) {
                return res.status(404).json((0, response_1.errorResponse)('未找到API配置'));
            }
            res.json((0, response_1.successResponse)(apiConfig, '获取API配置成功'));
        }
        catch (error) {
            console.error('获取API配置失败:', error);
            res.status(500).json((0, response_1.errorResponse)('获取API配置失败'));
        }
    }
}
exports.SystemConfigController = SystemConfigController;
__decorate([
    (0, systemLog_decorator_1.LogView)('系统配置'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], SystemConfigController.prototype, "getSystemConfig", null);
__decorate([
    (0, systemLog_decorator_1.LogUpdate)('系统配置'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], SystemConfigController.prototype, "saveSystemConfig", null);
__decorate([
    (0, systemLog_decorator_1.LogUpdate)('系统配置'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], SystemConfigController.prototype, "setConfig", null);
__decorate([
    (0, systemLog_decorator_1.LogUpdate)('系统配置'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], SystemConfigController.prototype, "updateConfigByKey", null);
__decorate([
    (0, systemLog_decorator_1.LogView)('系统配置'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], SystemConfigController.prototype, "getConfigByKey", null);
__decorate([
    (0, systemLog_decorator_1.LogDelete)('系统配置'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], SystemConfigController.prototype, "deleteConfig", null);
__decorate([
    (0, systemLog_decorator_1.LogView)('系统配置'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], SystemConfigController.prototype, "getAllConfigs", null);
__decorate([
    (0, systemLog_decorator_1.LogView)('系统配置'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], SystemConfigController.prototype, "getRegionMappings", null);
__decorate([
    (0, systemLog_decorator_1.LogView)('系统配置'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], SystemConfigController.prototype, "getApiConfig", null);
//# sourceMappingURL=system-config.controller.js.map