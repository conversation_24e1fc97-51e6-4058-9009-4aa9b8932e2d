"use strict";
/**
 * 基础仓库类
 * 提供通用的数据库操作方法
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseRepository = void 0;
class BaseRepository {
    constructor(prisma) {
        this.prisma = prisma;
    }
    /**
     * 构建分页查询参数
     */
    buildPaginationParams(page, pageSize) {
        return {
            skip: (page - 1) * pageSize,
            take: pageSize
        };
    }
    /**
     * 构建排序参数
     */
    buildOrderBy(sortBy, sortOrder = 'desc') {
        const orderBy = {};
        orderBy[sortBy] = sortOrder;
        return orderBy;
    }
    /**
     * 构建搜索条件
     */
    buildSearchCondition(search, fields) {
        if (!search)
            return {};
        return {
            OR: fields.map(field => ({
                [field]: { contains: search }
            }))
        };
    }
    /**
     * 构建日期范围条件
     */
    buildDateRangeCondition(startDate, endDate) {
        const condition = {};
        if (startDate) {
            condition.gte = new Date(startDate);
        }
        if (endDate) {
            const endDateTime = new Date(endDate);
            endDateTime.setHours(23, 59, 59, 999);
            condition.lte = endDateTime;
        }
        return Object.keys(condition).length > 0 ? condition : undefined;
    }
    /**
     * 构建数值范围条件
     */
    buildNumberRangeCondition(min, max) {
        const condition = {};
        if (min !== undefined) {
            condition.gte = min;
        }
        if (max !== undefined) {
            condition.lte = max;
        }
        return Object.keys(condition).length > 0 ? condition : undefined;
    }
}
exports.BaseRepository = BaseRepository;
//# sourceMappingURL=base.repository.js.map