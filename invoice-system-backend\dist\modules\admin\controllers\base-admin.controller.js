"use strict";
/**
 * 管理员基础控制器
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseAdminController = void 0;
const response_1 = require("../../../shared/utils/response");
class BaseAdminController {
    constructor(adminService) {
        this.adminService = adminService;
    }
    /**
     * 获取管理员仪表板统计数据
     */
    async getDashboardStats(req, res) {
        try {
            const stats = await this.adminService.getDashboardStats();
            res.json((0, response_1.successResponse)(stats, '获取仪表板统计数据成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 处理分页参数
     */
    getPaginationParams(req) {
        const page = parseInt(req.query.page) || 1;
        const pageSize = parseInt(req.query.pageSize) || parseInt(req.query.limit) || 10;
        const search = req.query.search;
        const status = req.query.status;
        return { page, pageSize, search, status };
    }
    /**
     * 处理ID参数
     */
    getIdParam(req) {
        const { id } = req.params;
        const numericId = parseInt(id);
        if (isNaN(numericId)) {
            throw new Error('无效的ID参数');
        }
        return numericId;
    }
}
exports.BaseAdminController = BaseAdminController;
//# sourceMappingURL=base-admin.controller.js.map