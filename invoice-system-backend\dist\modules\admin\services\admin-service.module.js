"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminServiceModule = void 0;
const dashboard_service_1 = require("./dashboard.service");
const user_service_1 = require("./user.service");
const merchant_service_1 = require("./merchant.service");
const company_service_1 = require("./company.service");
const invoice_service_1 = require("./invoice.service");
const task_service_1 = require("./task.service");
const system_log_service_1 = require("./system-log.service");
const export_service_1 = require("./export.service");
const invoice_staff_service_1 = require("../../invoice-staff/services/invoice-staff.service");
/**
 * 管理员服务模块 - 整合所有子服务
 */
class AdminServiceModule {
    constructor(prisma) {
        this.prisma = prisma;
        this.dashboardService = new dashboard_service_1.DashboardService(prisma);
        this.userService = new user_service_1.UserService(prisma);
        this.merchantService = new merchant_service_1.MerchantService(prisma);
        this.companyService = new company_service_1.CompanyService(prisma);
        this.invoiceService = new invoice_service_1.InvoiceService(prisma);
        this.taskService = new task_service_1.TaskService(prisma);
        this.systemLogService = new system_log_service_1.SystemLogService(prisma);
        this.exportService = new export_service_1.ExportService(prisma);
        this.invoiceStaffService = new invoice_staff_service_1.InvoiceStaffService(prisma);
    }
    // 仪表盘相关
    async getDashboardStats() {
        return this.dashboardService.getDashboardStats();
    }
    // 用户相关
    async getUserList(query) {
        return this.userService.getUserList(query);
    }
    async getUserDetail(id) {
        return this.userService.getUserDetail(id);
    }
    async createUser(data) {
        return this.userService.createUser(data);
    }
    async updateUser(id, data) {
        return this.userService.updateUser(id, data);
    }
    async deleteUser(id) {
        return this.userService.deleteUser(id);
    }
    async getUserStats() {
        return this.userService.getUserStats();
    }
    // 商家相关
    async getMerchantList(query) {
        return this.merchantService.getMerchantList(query);
    }
    async getMerchantDetail(id) {
        return this.merchantService.getMerchantDetail(id);
    }
    async createMerchant(data) {
        return this.merchantService.createMerchant(data);
    }
    async updateMerchant(id, data) {
        return this.merchantService.updateMerchant(id, data);
    }
    async deleteMerchant(id) {
        return await this.merchantService.deleteMerchant(id);
    }
    async getMerchantStats() {
        return this.merchantService.getMerchantStats();
    }
    // 公司相关
    async getCompanyList(query) {
        return this.companyService.getCompanies(query);
    }
    async getCompanyDetail(id) {
        return this.companyService.getCompanyById(id);
    }
    async createCompany(data) {
        return this.companyService.createCompany(data);
    }
    async updateCompany(id, data) {
        return this.companyService.updateCompany(id, data);
    }
    async deleteCompany(id) {
        return this.companyService.deleteCompany(id);
    }
    async getCompanyStats() {
        return this.companyService.getCompanyStats();
    }
    // 开票员相关
    async getInvoiceStaffList(query) {
        return this.invoiceStaffService.getInvoiceStaffList(query);
    }
    async getInvoiceStaffDetail(id) {
        return this.invoiceStaffService.getInvoiceStaffDetail(id);
    }
    async createInvoiceStaff(data) {
        // 首先创建本地开票员记录
        const invoiceStaff = await this.invoiceStaffService.createInvoiceStaff(data);
        // 然后调用外部接口创建开票员
        try {
            const { ExternalDrawerService } = await Promise.resolve().then(() => __importStar(require('./external-drawer.service')));
            const externalDrawerService = new ExternalDrawerService(this.prisma);
            const externalResult = await externalDrawerService.createDrawer({
                drawer_name: data.staffName,
                id_number: data.idCardNumber,
                phone_number: data.phone,
                login_pwd: data.taxLoginPassword // 使用 taxLoginPassword 作为 login_pwd
            });
            if (externalResult.success && externalResult.drawer_code) {
                // 更新本地记录，保存外部系统返回的开票员编号
                await this.prisma.invoiceStaff.update({
                    where: { id: invoiceStaff.id },
                    data: {
                        externalStaffId: externalResult.drawer_code,
                        status: 'BOUND'
                    }
                });
                console.log(`✅ 开票员创建成功，外部编号: ${externalResult.drawer_code}`);
            }
            else {
                // 外部接口调用失败，更新状态为绑定失败
                await this.prisma.invoiceStaff.update({
                    where: { id: invoiceStaff.id },
                    data: {
                        status: 'BIND_FAILED',
                        failReason: externalResult.message
                    }
                });
                console.log(`⚠️ 外部接口调用失败: ${externalResult.message}`);
            }
        }
        catch (error) {
            console.error('调用外部接口异常:', error);
            // 更新状态为绑定失败
            await this.prisma.invoiceStaff.update({
                where: { id: invoiceStaff.id },
                data: {
                    status: 'BIND_FAILED',
                    failReason: `外部接口调用异常: ${error instanceof Error ? error.message : '未知错误'}`
                }
            });
        }
        return invoiceStaff;
    }
    async updateInvoiceStaff(id, data) {
        return this.invoiceStaffService.updateInvoiceStaff(id, data);
    }
    async deleteInvoiceStaff(id) {
        const result = await this.invoiceStaffService.deleteInvoiceStaff(id);
        return;
    }
    async getInvoiceStaffStats() {
        return this.invoiceStaffService.getInvoiceStaffStats();
    }
    // 发票相关
    async getInvoiceList(query) {
        return this.invoiceService.getInvoiceList(query);
    }
    async getInvoiceDetail(id) {
        return this.invoiceService.getInvoiceDetail(id);
    }
    async createInvoice(data) {
        return this.invoiceService.createInvoice(data);
    }
    async updateInvoice(id, data) {
        return this.invoiceService.updateInvoice(id, data);
    }
    async deleteInvoice(id) {
        return this.invoiceService.deleteInvoice(id);
    }
    async updateInvoiceStatus(id, status) {
        return this.invoiceService.updateInvoiceStatus(id, status);
    }
    // 任务相关
    async getTaskList(query) {
        return this.taskService.getTaskList(query);
    }
    // 系统日志相关
    async getSystemLogList(query) {
        return this.systemLogService.getSystemLogList(query);
    }
    async createSystemLog(data) {
        return this.systemLogService.createSystemLog(data);
    }
    async getSystemLogDetail(id) {
        return this.systemLogService.getSystemLogDetail(id);
    }
    async getSystemLogStats() {
        return this.systemLogService.getSystemLogStats();
    }
    async getActionStats(days) {
        return this.systemLogService.getActionStats(days);
    }
    async cleanupOldLogs(days) {
        return this.systemLogService.cleanupOldLogs(days);
    }
    async getLogLevelStats(days) {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        // 使用原始查询来获取按级别统计的数据
        const levelStats = await this.prisma.$queryRaw `
      SELECT level, COUNT(*) as count 
      FROM system_logs 
      WHERE createdAt >= ${startDate}
      GROUP BY level
      ORDER BY count DESC
    `;
        return levelStats.map(stat => ({
            level: stat.level,
            count: Number(stat.count)
        }));
    }
    async getUserActionRanking(days, userType) {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        const where = {
            createdAt: {
                gte: startDate
            }
        };
        if (userType && userType !== 'all') {
            where.userType = userType;
        }
        const ranking = await this.prisma.systemLog.groupBy({
            by: ['adminId', 'merchantId', 'userId', 'userType'],
            where,
            _count: {
                id: true
            },
            orderBy: {
                _count: {
                    id: 'desc'
                }
            },
            take: 20
        });
        // 获取用户详细信息
        const enrichedRanking = await Promise.all(ranking.map(async (item) => {
            let userInfo = null;
            if (item.adminId) {
                userInfo = await this.prisma.admin.findUnique({
                    where: { id: item.adminId },
                    select: { username: true, role: true }
                });
            }
            else if (item.merchantId) {
                userInfo = await this.prisma.merchant.findUnique({
                    where: { id: item.merchantId },
                    select: { username: true, membershipLevel: true }
                });
            }
            else if (item.userId) {
                userInfo = await this.prisma.user.findUnique({
                    where: { id: item.userId },
                    select: { username: true, membershipLevel: true }
                });
            }
            return {
                userId: item.adminId || item.merchantId || item.userId,
                userType: item.userType,
                username: userInfo?.username || '未知用户',
                userRole: userInfo?.role || userInfo?.membershipLevel || '',
                actionCount: item._count.id
            };
        }));
        return enrichedRanking;
    }
    async getLogTrends(days, level) {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        const where = {
            createdAt: {
                gte: startDate
            }
        };
        if (level && level !== 'all') {
            where.level = level;
        }
        // 按天统计日志数量 - 使用原始查询获取level字段
        let query = `
      SELECT createdAt, level FROM system_logs 
      WHERE createdAt >= ?
    `;
        const params = [startDate];
        if (level && level !== 'all') {
            query += ` AND level = ?`;
            params.push(level);
        }
        query += ` ORDER BY createdAt ASC`;
        const trends = await this.prisma.$queryRawUnsafe(query, ...params);
        // 按日期分组统计
        const trendMap = new Map();
        trends.forEach(log => {
            const date = log.createdAt.toISOString().split('T')[0];
            if (!trendMap.has(date)) {
                trendMap.set(date, { ERROR: 0, WARN: 0, INFO: 0, DEBUG: 0, total: 0 });
            }
            const dayStats = trendMap.get(date);
            dayStats[log.level]++;
            dayStats.total++;
        });
        // 转换为数组格式
        const result = Array.from(trendMap.entries()).map(([date, stats]) => ({
            date,
            ...stats
        })).sort((a, b) => a.date.localeCompare(b.date));
        return result;
    }
    // 导出相关
    async exportData(type, format, filters) {
        return this.exportService.exportData(type, format, filters);
    }
}
exports.AdminServiceModule = AdminServiceModule;
//# sourceMappingURL=admin-service.module.js.map