"use strict";
/**
 * 开票员外部API集成服务
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExternalDrawerService = void 0;
const drawer_api_service_1 = require("../../external/services/drawer-api.service");
class ExternalDrawerService {
    constructor(prisma) {
        this.prisma = prisma;
        this.drawerApiService = drawer_api_service_1.DrawerApiService.getInstance();
    }
    /**
     * 创建开票员到外部系统
     */
    async createDrawer(drawerData) {
        try {
            console.log(`🔄 [开票员服务] 开始创建开票员到外部系统: ${drawerData.drawer_name}`);
            // 调用外部API创建开票员
            const response = await this.drawerApiService.createDrawer({
                drawer_name: drawerData.drawer_name,
                id_number: drawerData.id_number,
                phone_number: drawerData.phone_number,
                login_pwd: drawerData.login_pwd
            });
            console.log('🔄 [开票员服务] 外部API响应:', response);
            if (response.code === 200 && response.data?.drawer_code) {
                const drawerCode = response.data.drawer_code;
                console.log(`✅ [开票员服务] 创建成功，开票员编号: ${drawerCode}`);
                // 记录操作日志
                await this.prisma.systemLog.create({
                    data: {
                        userType: 'ADMIN',
                        action: 'CREATE_DRAWER',
                        resource: 'drawer',
                        details: JSON.stringify({
                            drawerName: drawerData.drawer_name,
                            idNumber: drawerData.id_number,
                            phoneNumber: drawerData.phone_number,
                            drawerCode: drawerCode,
                            response: response
                        })
                    }
                });
                return {
                    success: true,
                    drawer_code: drawerCode,
                    message: '开票员创建成功'
                };
            }
            else {
                // 外部API返回业务错误（如40002证件号码有误）
                console.log(`⚠️ [开票员服务] 外部API业务错误: 状态码=${response.code}, 消息=${response.msg}`);
                await this.prisma.systemLog.create({
                    data: {
                        userType: 'ADMIN',
                        action: 'CREATE_DRAWER_BUSINESS_FAILED',
                        resource: 'drawer',
                        details: JSON.stringify({
                            drawerName: drawerData.drawer_name,
                            idNumber: drawerData.id_number,
                            phoneNumber: drawerData.phone_number,
                            response: response
                        })
                    }
                });
                return {
                    success: false,
                    message: response.msg || '外部系统业务处理失败',
                    errorType: 'EXTERNAL_API_BUSINESS'
                };
            }
        }
        catch (error) {
            console.error('💥 [开票员服务] 创建开票员异常:', error);
            // 记录错误日志
            await this.prisma.systemLog.create({
                data: {
                    userType: 'ADMIN',
                    action: 'CREATE_DRAWER_EXCEPTION',
                    resource: 'drawer',
                    details: JSON.stringify({
                        drawerName: drawerData.drawer_name,
                        idNumber: drawerData.id_number,
                        phoneNumber: drawerData.phone_number,
                        error: error instanceof Error ? error.message : '未知错误'
                    })
                }
            });
            return {
                success: false,
                message: `系统异常: ${error instanceof Error ? error.message : '未知错误'}`,
                errorType: 'EXTERNAL_API_ERROR'
            };
        }
    }
    /**
     * 绑定开票员到公司并同步外部API
     */
    async bindDrawerWithSync(staffId, companyId) {
        try {
            console.log(`🔄 [开票员服务] 开始绑定开票员: staffId=${staffId}, companyId=${companyId}`);
            // 1. 获取开票员和公司信息
            const staff = await this.prisma.invoiceStaff.findUnique({
                where: { id: staffId }
            });
            const company = await this.prisma.company.findUnique({
                where: { id: companyId }
            });
            if (!staff) {
                console.log(`ℹ️ [开票员服务] 开票员不存在: ${staffId}`);
                return {
                    success: false,
                    message: '开票员不存在',
                    errorType: 'BACKEND_VALIDATION'
                };
            }
            if (!company) {
                console.log(`ℹ️ [开票员服务] 公司不存在: ${companyId}`);
                return {
                    success: false,
                    message: '公司不存在',
                    errorType: 'BACKEND_VALIDATION'
                };
            }
            if (!staff.externalStaffId) {
                console.log(`ℹ️ [开票员服务] 开票员未同步到外部API: ${staffId}`);
                return {
                    success: false,
                    message: '开票员未同步到外部API，请先创建开票员',
                    errorType: 'BACKEND_VALIDATION'
                };
            }
            if (!company.externalCompanyId) {
                console.log(`ℹ️ [开票员服务] 公司未同步到外部API: ${companyId}`);
                return {
                    success: false,
                    message: '公司未同步到外部API，请先创建公司',
                    errorType: 'BACKEND_VALIDATION'
                };
            }
            // 2. 调用外部API绑定开票员
            console.log(`🔗 [开票员服务] 调用外部API绑定开票员: company_id=${company.externalCompanyId}, drawer_code=${staff.externalStaffId}`);
            const response = await this.drawerApiService.bindDrawer({
                company_id: company.externalCompanyId,
                login_account: company.legalRepresentativeIdCardNumber, // 法人身份证号码作为登录账号
                login_password: staff.taxLoginPassword, // 使用开票员的税局登录密码
                drawer_code: staff.externalStaffId,
                need_consign_file: 0,
                auto_renewal: 1
            });
            console.log('🔄 [开票员服务] 外部API绑定响应:', response);
            if (response.code === 200) {
                // 3. 更新数据库绑定状态
                console.log('💾 [开票员服务] 更新数据库绑定状态');
                const updatedStaff = await this.prisma.invoiceStaff.update({
                    where: { id: staffId },
                    data: {
                        companyId: companyId,
                        status: 'BOUND',
                        boundAt: new Date()
                    }
                });
                // 记录操作日志
                await this.prisma.systemLog.create({
                    data: {
                        userType: 'ADMIN',
                        action: 'BIND_DRAWER',
                        resource: 'drawer',
                        details: JSON.stringify({
                            staffId: staffId,
                            companyId: companyId,
                            drawerCode: staff.externalStaffId,
                            companyExternalId: company.externalCompanyId,
                            response: response
                        })
                    }
                });
                console.log(`✅ [开票员服务] 绑定成功`);
                return {
                    success: true,
                    data: updatedStaff,
                    message: '开票员绑定成功'
                };
            }
            else {
                // 外部API返回业务错误
                console.log(`⚠️ [开票员服务] 外部API绑定业务错误: 状态码=${response.code}, 消息=${response.msg}`);
                await this.prisma.systemLog.create({
                    data: {
                        userType: 'ADMIN',
                        action: 'BIND_DRAWER_BUSINESS_FAILED',
                        resource: 'drawer',
                        details: JSON.stringify({
                            staffId: staffId,
                            companyId: companyId,
                            drawerCode: staff.externalStaffId,
                            companyExternalId: company.externalCompanyId,
                            response: response
                        })
                    }
                });
                return {
                    success: false,
                    message: response.msg || '外部系统绑定失败',
                    errorType: 'EXTERNAL_API_BUSINESS'
                };
            }
        }
        catch (error) {
            console.error('💥 [开票员服务] 绑定开票员异常:', error);
            // 记录错误日志
            await this.prisma.systemLog.create({
                data: {
                    userType: 'ADMIN',
                    action: 'BIND_DRAWER_EXCEPTION',
                    resource: 'drawer',
                    details: JSON.stringify({
                        staffId: staffId,
                        companyId: companyId,
                        error: error instanceof Error ? error.message : '未知错误'
                    })
                }
            });
            return {
                success: false,
                message: `系统异常: ${error instanceof Error ? error.message : '未知错误'}`,
                errorType: 'EXTERNAL_API_ERROR'
            };
        }
    }
}
exports.ExternalDrawerService = ExternalDrawerService;
//# sourceMappingURL=external-drawer.service.js.map