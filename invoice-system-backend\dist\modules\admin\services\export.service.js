"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExportService = void 0;
/**
 * 导出服务
 */
const base_service_1 = require("./base.service");
const date_fns_1 = require("date-fns");
const errorHandler_1 = require("../../../shared/middleware/errorHandler");
class ExportService extends base_service_1.BaseService {
    /**
     * 导出数据
     */
    async exportData(type, format, filters) {
        let data = [];
        let filename = '';
        let headers = [];
        switch (type) {
            case 'merchants':
                const merchants = await this.prisma.merchant.findMany({
                    include: {
                        _count: {
                            select: {
                                companies: true,
                                invoices: true
                            }
                        }
                    }
                });
                data = merchants.map(merchant => ({
                    商家ID: merchant.id,
                    用户名: merchant.username,
                    邮箱: merchant.email || '',
                    手机号: merchant.phone || '',
                    状态: merchant.status === 'ACTIVE' ? '正常' : '禁用',
                    会员等级: merchant.membershipLevel,
                    公司数量: merchant._count.companies,
                    发票数量: merchant._count.invoices,
                    注册时间: (0, date_fns_1.format)(merchant.registeredAt, 'yyyy-MM-dd HH:mm:ss')
                }));
                filename = `merchants_${(0, date_fns_1.format)(new Date(), 'yyyyMMdd_HHmmss')}`;
                headers = ['商家ID', '用户名', '邮箱', '手机号', '状态', '会员等级', '公司数量', '发票数量', '注册时间'];
                break;
            case 'users':
                const users = await this.prisma.user.findMany({
                    include: {
                        _count: {
                            select: {
                                invoices: true
                            }
                        }
                    }
                });
                data = users.map(user => ({
                    用户ID: user.id,
                    用户名: user.username || '',
                    邮箱: user.email || '',
                    手机号: user.phone || '',
                    状态: '正常',
                    发票数量: user._count.invoices,
                    注册时间: (0, date_fns_1.format)(user.createdAt, 'yyyy-MM-dd HH:mm:ss')
                }));
                filename = `users_${(0, date_fns_1.format)(new Date(), 'yyyyMMdd_HHmmss')}`;
                headers = ['用户ID', '用户名', '邮箱', '手机号', '状态', '发票数量', '注册时间'];
                break;
            case 'companies':
                const companies = await this.prisma.company.findMany({
                    include: {
                        merchant: {
                            select: {
                                username: true
                            }
                        },
                        _count: {
                            select: {
                                invoiceStaffs: true,
                                invoices: true
                            }
                        }
                    }
                });
                data = companies.map(company => ({
                    公司ID: company.id,
                    公司名称: company.name,
                    税号: company.taxNumber,
                    地址: company.region || '',
                    电话: '',
                    状态: company.status === 'ACTIVE' ? '正常' : company.status === 'INACTIVE' ? '未开通' : '已到期',
                    所属商家: company.merchant.username,
                    开票员数量: company._count.invoiceStaffs,
                    发票数量: company._count.invoices,
                    创建时间: (0, date_fns_1.format)(company.createdAt, 'yyyy-MM-dd HH:mm:ss')
                }));
                filename = `companies_${(0, date_fns_1.format)(new Date(), 'yyyyMMdd_HHmmss')}`;
                headers = [
                    '公司ID',
                    '公司名称',
                    '税号',
                    '地址',
                    '电话',
                    '状态',
                    '所属商家',
                    '开票员数量',
                    '发票数量',
                    '创建时间'
                ];
                break;
            default:
                throw new errorHandler_1.AppError('不支持的导出类型', 400);
        }
        return { data, filename, headers };
    }
}
exports.ExportService = ExportService;
//# sourceMappingURL=export.service.js.map