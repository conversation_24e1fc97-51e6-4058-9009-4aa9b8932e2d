"use strict";
/**
 * 管理员主控制器 - 整合所有子控制器
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminController = void 0;
// 导入所有子控制器
const base_admin_controller_1 = require("./base-admin.controller");
const user_admin_controller_1 = require("./user-admin.controller");
const merchant_admin_controller_1 = require("./merchant-admin.controller");
const company_admin_controller_1 = require("./company-admin.controller");
const invoice_staff_admin_controller_1 = require("./invoice-staff-admin.controller");
const invoice_admin_controller_1 = require("./invoice-admin.controller");
const system_admin_controller_1 = require("./system-admin.controller");
class AdminController extends base_admin_controller_1.BaseAdminController {
    constructor(adminService) {
        super(adminService);
        // 初始化所有子控制器
        this.userController = new user_admin_controller_1.UserAdminController(adminService);
        this.merchantController = new merchant_admin_controller_1.MerchantAdminController(adminService);
        this.companyController = new company_admin_controller_1.CompanyAdminController(adminService);
        this.invoiceStaffController = new invoice_staff_admin_controller_1.InvoiceStaffAdminController(adminService);
        this.invoiceController = new invoice_admin_controller_1.InvoiceAdminController(adminService);
        this.systemController = new system_admin_controller_1.SystemAdminController(adminService);
    }
    // ==================== 仪表板相关 ====================
    async getDashboardStats(req, res) {
        return super.getDashboardStats(req, res);
    }
    // ==================== 用户管理相关 ====================
    async getUserList(req, res) {
        return this.userController.getUserList(req, res);
    }
    async getUserDetail(req, res) {
        return this.userController.getUserDetail(req, res);
    }
    async createUser(req, res) {
        return this.userController.createUser(req, res);
    }
    async updateUser(req, res) {
        return this.userController.updateUser(req, res);
    }
    async deleteUser(req, res) {
        return this.userController.deleteUser(req, res);
    }
    async getUserStats(req, res) {
        return this.userController.getUserStats(req, res);
    }
    // ==================== 商家管理相关 ====================
    async getMerchantList(req, res) {
        return this.merchantController.getMerchantList(req, res);
    }
    async getMerchantDetail(req, res) {
        return this.merchantController.getMerchantDetail(req, res);
    }
    async createMerchant(req, res) {
        return this.merchantController.createMerchant(req, res);
    }
    async updateMerchant(req, res) {
        return this.merchantController.updateMerchant(req, res);
    }
    async deleteMerchant(req, res) {
        return this.merchantController.deleteMerchant(req, res);
    }
    async getMerchantStats(req, res) {
        return this.merchantController.getMerchantList(req, res);
    }
    // ==================== 公司管理相关 ====================
    async getCompanyList(req, res) {
        return this.companyController.getCompanyList(req, res);
    }
    async getCompanyDetail(req, res) {
        return this.companyController.getCompanyDetail(req, res);
    }
    async createCompany(req, res) {
        return this.companyController.createCompany(req, res);
    }
    async updateCompany(req, res) {
        return this.companyController.updateCompany(req, res);
    }
    async deleteCompany(req, res) {
        return this.companyController.deleteCompany(req, res);
    }
    async syncDeleteCompany(req, res) {
        return this.companyController.syncDeleteCompany(req, res);
    }
    async getCompanyStats(req, res) {
        return this.companyController.getCompanyStats(req, res);
    }
    // ==================== 发票员工管理相关 ====================
    async getInvoiceStaffList(req, res) {
        return this.invoiceStaffController.getInvoiceStaffList(req, res);
    }
    async getInvoiceStaffDetail(req, res) {
        return this.invoiceStaffController.getInvoiceStaffDetail(req, res);
    }
    async createInvoiceStaff(req, res) {
        return this.invoiceStaffController.createInvoiceStaff(req, res);
    }
    async updateInvoiceStaff(req, res) {
        return this.invoiceStaffController.updateInvoiceStaff(req, res);
    }
    async deleteInvoiceStaff(req, res) {
        return this.invoiceStaffController.deleteInvoiceStaff(req, res);
    }
    async getInvoiceStaffStats(req, res) {
        return this.invoiceStaffController.getInvoiceStaffStats(req, res);
    }
    async bindInvoiceStaff(req, res) {
        return this.invoiceStaffController.bindInvoiceStaff(req, res);
    }
    async createDrawer(req, res) {
        return this.invoiceStaffController.createDrawer(req, res);
    }
    // ==================== 发票管理相关 ====================
    async getInvoiceList(req, res) {
        return this.invoiceController.getInvoiceList(req, res);
    }
    async getInvoiceDetail(req, res) {
        return this.invoiceController.getInvoiceDetail(req, res);
    }
    async createInvoice(req, res) {
        return this.invoiceController.createInvoice(req, res);
    }
    async updateInvoice(req, res) {
        return this.invoiceController.updateInvoice(req, res);
    }
    async deleteInvoice(req, res) {
        return this.invoiceController.deleteInvoice(req, res);
    }
    async updateInvoiceStatus(req, res) {
        return this.invoiceController.updateInvoiceStatus(req, res);
    }
    async getInvoiceTitleList(req, res) {
        return this.invoiceController.getInvoiceList(req, res);
    }
    async getInvoiceTitleDetail(req, res) {
        return this.invoiceController.getInvoiceDetail(req, res);
    }
    async createInvoiceTitle(req, res) {
        return this.invoiceController.getInvoiceList(req, res);
    }
    async updateInvoiceTitle(req, res) {
        return this.invoiceController.updateInvoiceStatus(req, res);
    }
    async deleteInvoiceTitle(req, res) {
        return this.invoiceController.getInvoiceList(req, res);
    }
    // ==================== 系统管理相关 ====================
    async getTaskList(req, res) {
        return this.systemController.getTaskList(req, res);
    }
    async getSystemLogList(req, res) {
        return this.systemController.getSystemLogList(req, res);
    }
    async createSystemLog(req, res) {
        return this.systemController.createSystemLog(req, res);
    }
    async getSystemLogDetail(req, res) {
        return this.systemController.getSystemLogDetail(req, res);
    }
    async getSystemLogStats(req, res) {
        return this.systemController.getSystemLogStats(req, res);
    }
    async getActionStats(req, res) {
        return this.systemController.getActionStats(req, res);
    }
    async getLogLevelStats(req, res) {
        return this.systemController.getLogLevelStats(req, res);
    }
    async getUserActionRanking(req, res) {
        return this.systemController.getUserActionRanking(req, res);
    }
    async getLogTrends(req, res) {
        return this.systemController.getLogTrends(req, res);
    }
    async cleanupOldLogs(req, res) {
        return this.systemController.cleanupOldLogs(req, res);
    }
    async exportData(req, res) {
        return this.systemController.exportData(req, res);
    }
}
exports.AdminController = AdminController;
//# sourceMappingURL=admin.controller.js.map