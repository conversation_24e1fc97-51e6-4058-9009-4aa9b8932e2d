import { PrismaClient } from '@prisma/client';
export interface TaxCodeFilters {
    search?: string;
    isActive?: boolean;
    isSummary?: boolean;
    page?: number;
    pageSize?: number;
}
export interface TaxCodeData {
    categoryName: string;
    categoryCode: string;
    categoryShortCode?: string;
    isSummary: boolean;
    isActive: boolean;
    shortName?: string;
    description?: string;
    keywords?: string;
}
export declare class TaxCodeService {
    private prisma;
    constructor(prisma: PrismaClient);
    findAll(filters: TaxCodeFilters): Promise<{
        items: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            description: string | null;
            keywords: string | null;
            categoryCode: string;
            categoryName: string;
            categoryShortCode: string | null;
            isSummary: boolean;
            isActive: boolean;
            shortName: string | null;
        }[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    }>;
    findById(id: number): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        description: string | null;
        keywords: string | null;
        categoryCode: string;
        categoryName: string;
        categoryShortCode: string | null;
        isSummary: boolean;
        isActive: boolean;
        shortName: string | null;
    } | null>;
    create(data: TaxCodeData): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        description: string | null;
        keywords: string | null;
        categoryCode: string;
        categoryName: string;
        categoryShortCode: string | null;
        isSummary: boolean;
        isActive: boolean;
        shortName: string | null;
    }>;
    update(id: number, data: Partial<TaxCodeData>): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        description: string | null;
        keywords: string | null;
        categoryCode: string;
        categoryName: string;
        categoryShortCode: string | null;
        isSummary: boolean;
        isActive: boolean;
        shortName: string | null;
    }>;
    delete(id: number): Promise<{
        success: boolean;
    }>;
    search(keyword: string, limit?: number): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        description: string | null;
        keywords: string | null;
        categoryCode: string;
        categoryName: string;
        categoryShortCode: string | null;
        isSummary: boolean;
        isActive: boolean;
        shortName: string | null;
    }[]>;
    batchCreate(taxCodes: TaxCodeData[]): Promise<{
        success: number;
        failed: number;
        errors: string[];
    }>;
}
//# sourceMappingURL=tax-code.service.d.ts.map