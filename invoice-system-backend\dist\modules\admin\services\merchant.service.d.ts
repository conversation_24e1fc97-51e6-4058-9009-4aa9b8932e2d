/**
 * 商家服务
 */
import { BaseService } from './base.service';
import { PaginationQuery, MerchantStats } from '../../../shared/interfaces/types';
export declare class MerchantService extends BaseService {
    /**
     * 获取商家列表
     */
    getMerchantList(query: PaginationQuery & {
        search?: string;
        status?: string;
        membershipLevel?: string;
    }): Promise<{
        merchants: any[];
        total: number;
    }>;
    /**
     * 获取商家详情
     */
    getMerchantDetail(id: number): Promise<any>;
    /**
     * 创建商家
     */
    createMerchant(data: {
        username?: string;
        email?: string;
        phone?: string;
        password: string;
        membershipLevel?: string;
        membershipExpiry?: string;
    }): Promise<any>;
    /**
     * 更新商家
     */
    updateMerchant(id: number, data: {
        email?: string;
        phone?: string;
        status?: 'ACTIVE' | 'BANNED';
        membershipLevel?: string;
        membershipExpiry?: string;
    }): Promise<any>;
    /**
     * 删除商家
     */
    deleteMerchant(id: number): Promise<{
        success: boolean;
        message?: string;
        data?: any;
    }>;
    /**
     * 获取商家统计信息
     */
    getMerchantStats(): Promise<MerchantStats>;
}
//# sourceMappingURL=merchant.service.d.ts.map