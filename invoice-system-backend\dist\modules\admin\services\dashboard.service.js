"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DashboardService = void 0;
/**
 * 仪表盘服务
 */
const base_service_1 = require("./base.service");
class DashboardService extends base_service_1.BaseService {
    /**
     * 获取管理员仪表板统计数据
     */
    async getDashboardStats() {
        const { today, thisMonth } = this.getDateRanges();
        const [totalMerchants, totalUsers, totalCompanies, totalInvoices, todayInvoices, thisMonthInvoices, pendingTasks, failedTasks] = await Promise.all([
            // 总商家数
            this.prisma.merchant.count(),
            // 总用户数
            this.prisma.user.count(),
            // 总公司数
            this.prisma.company.count(),
            // 总发票数
            this.prisma.invoice.count(),
            // 今日发票数
            this.prisma.invoice.count({
                where: {
                    createdAt: {
                        gte: today
                    }
                }
            }),
            // 本月发票数
            this.prisma.invoice.count({
                where: {
                    createdAt: {
                        gte: thisMonth
                    }
                }
            }),
            // 待处理任务数
            this.prisma.task.count({
                where: {
                    status: 'PENDING'
                }
            }),
            // 失败任务数
            this.prisma.task.count({
                where: {
                    status: 'FAILED'
                }
            })
        ]);
        return {
            totalMerchants,
            totalUsers,
            totalCompanies,
            totalInvoices,
            todayInvoices,
            pendingTasks,
            failedTasks,
            totalAmount: 0,
            todayAmount: 0,
            activeUsers: totalUsers,
            recentActivities: []
        };
    }
}
exports.DashboardService = DashboardService;
//# sourceMappingURL=dashboard.service.js.map