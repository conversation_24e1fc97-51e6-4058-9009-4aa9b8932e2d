"use strict";
/**
 * 公司外部API集成服务
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExternalCompanyService = void 0;
const company_api_service_1 = require("../../external/services/company-api.service");
const errorHandler_1 = require("../../../shared/middleware/errorHandler");
class ExternalCompanyService {
    constructor(prisma) {
        this.prisma = prisma;
        this.companyApiService = company_api_service_1.CompanyApiService.getInstance();
    }
    /**
     * 同步创建公司到外部系统
     */
    async syncCreateCompany(companyId) {
        try {
            // 获取公司信息
            const company = await this.prisma.company.findUnique({
                where: { id: companyId },
                include: {
                    merchant: {
                        select: {
                            id: true,
                            username: true
                        }
                    }
                }
            });
            if (!company) {
                throw new errorHandler_1.AppError('公司不存在', 404);
            }
            // 移除已同步检查，允许重复绑定操作
            console.log(`🔄 开始同步公司到外部系统: ${company.name} (ID: ${companyId})`);
            if (company.externalCompanyId) {
                console.log(`⚠️ 公司已有外部ID: ${company.externalCompanyId}，将更新为新的外部ID`);
            }
            // 调用外部API创建公司
            const response = await this.companyApiService.createCompany({
                company_name: company.name,
                tax_no: company.taxNumber,
                tax_area_code: company.regionCode,
                notify_url: process.env.EXTERNAL_API_NOTIFY_URL || 'https://your-domain.com/api/v1/external/callback'
            });
            if (response.code === 200 && response.data?.company_id) {
                const newExternalId = response.data.company_id;
                const wasAlreadyBound = !!company.externalCompanyId;
                const oldExternalId = company.externalCompanyId;
                // 更新本地数据库，保存外部公司ID，设置状态为已开通，有效期为一年后
                const oneYearLater = new Date();
                oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);
                await this.prisma.company.update({
                    where: { id: companyId },
                    data: {
                        externalCompanyId: newExternalId,
                        status: 'ACTIVE',
                        validityPeriod: oneYearLater
                    }
                });
                // 记录操作日志
                await this.prisma.systemLog.create({
                    data: {
                        userType: 'ADMIN',
                        action: wasAlreadyBound ? 'SYNC_UPDATE_COMPANY' : 'SYNC_CREATE_COMPANY',
                        resource: 'company',
                        details: JSON.stringify({
                            companyId: companyId,
                            companyName: company.name,
                            oldExternalCompanyId: oldExternalId,
                            newExternalCompanyId: newExternalId,
                            wasAlreadyBound: wasAlreadyBound,
                            response: response
                        })
                    }
                });
                return {
                    success: true,
                    externalCompanyId: newExternalId,
                    message: wasAlreadyBound ? '公司外部ID更新成功' : '公司同步创建成功'
                };
            }
            else {
                throw new errorHandler_1.AppError(`外部API创建公司失败: ${response.msg}`, 500);
            }
        }
        catch (error) {
            console.error('同步创建公司失败:', error);
            // 记录错误日志
            await this.prisma.systemLog.create({
                data: {
                    userType: 'ADMIN',
                    action: 'SYNC_CREATE_COMPANY_FAILED',
                    resource: 'company',
                    details: JSON.stringify({
                        companyId: companyId,
                        error: error instanceof Error ? error.message : '未知错误'
                    })
                }
            });
            return {
                success: false,
                message: error instanceof Error ? error.message : '同步创建公司失败'
            };
        }
    }
    /**
     * 从外部系统删除公司
     */
    async syncDeleteCompany(companyId) {
        console.log(`🔄 [同步删除] 开始处理公司ID: ${companyId}`);
        try {
            // 1. 后端校验 - 获取公司信息
            console.log(`📋 [后端校验] 查询公司信息...`);
            const company = await this.prisma.company.findUnique({
                where: { id: companyId },
                include: {
                    merchant: {
                        select: {
                            id: true,
                            username: true
                        }
                    }
                }
            });
            if (!company) {
                console.log(`❌ [后端校验] 公司不存在: ID=${companyId}`);
                return {
                    success: false,
                    message: '公司不存在',
                    errorType: 'BACKEND_VALIDATION'
                };
            }
            console.log(`✅ [后端校验] 公司信息: ${company.name} (外部ID: ${company.externalCompanyId || '未绑定'})`);
            // 2. 后端校验 - 检查是否有关联的开票员
            console.log(`📊 [后端校验] 检查关联开票员...`);
            const staffCount = await this.prisma.invoiceStaff.count({
                where: { companyId: companyId }
            });
            if (staffCount > 0) {
                console.log(`❌ [后端校验] 发现关联开票员: ${staffCount}个`);
                return {
                    success: false,
                    message: '该公司下还有开票员，请先删除所有开票员后再同步删除',
                    errorType: 'BACKEND_VALIDATION'
                };
            }
            console.log(`✅ [后端校验] 所有校验通过，开始调用外部API`);
            // 3. 调用外部API删除公司（如果有外部ID）
            let deleteResponse = null;
            if (company.externalCompanyId) {
                console.log(`🌐 [外部API] 调用删除接口: 外部ID=${company.externalCompanyId}`);
                deleteResponse = await this.companyApiService.deleteCompany(company.externalCompanyId);
                console.log(`📡 [外部API] 响应结果:`, {
                    code: deleteResponse.code,
                    msg: deleteResponse.msg
                });
                // 4. 处理外部API响应
                if (deleteResponse.code !== 200) {
                    // 如果公司不存在，视为删除成功（幂等操作）
                    if ((deleteResponse.code === 500 && deleteResponse.msg === '未找到公司信息') ||
                        (deleteResponse.code === 404 && deleteResponse.msg === '资源不存在')) {
                        console.log(`⚠️ [外部API] 公司不存在，视为删除成功`);
                    }
                    else {
                        // 其他错误情况
                        console.log(`⚠️ [外部API] 业务逻辑错误: ${deleteResponse.msg}`);
                        // 记录日志
                        await this.prisma.systemLog.create({
                            data: {
                                userType: 'ADMIN',
                                action: 'SYNC_DELETE_COMPANY_EXTERNAL_BUSINESS_ERROR',
                                resource: 'company',
                                details: JSON.stringify({
                                    companyId: companyId,
                                    companyName: company.name,
                                    externalCompanyId: company.externalCompanyId,
                                    externalResponse: deleteResponse,
                                    errorType: 'EXTERNAL_API_BUSINESS'
                                })
                            }
                        });
                        return {
                            success: false,
                            message: deleteResponse.msg || '外部系统业务处理失败',
                            errorType: 'EXTERNAL_API_BUSINESS'
                        };
                    }
                }
            }
            else {
                console.log(`⚠️ [外部API] 公司未绑定外部系统，跳过外部API调用`);
            }
            // 5. 成功处理 - 清除本地的外部公司ID，设置状态为未开通，清空有效期
            console.log(`✅ [数据库] 清除本地外部公司ID绑定，更新状态为未开通`);
            await this.prisma.company.update({
                where: { id: companyId },
                data: {
                    externalCompanyId: null,
                    status: 'INACTIVE',
                    validityPeriod: null
                }
            });
            // 6. 记录成功日志
            const isCompanyNotFound = deleteResponse && ((deleteResponse.code === 500 && deleteResponse.msg === '未找到公司信息') ||
                (deleteResponse.code === 404 && deleteResponse.msg === '资源不存在'));
            const logAction = isCompanyNotFound
                ? 'SYNC_DELETE_COMPANY_NOT_FOUND'
                : 'SYNC_DELETE_COMPANY_SUCCESS';
            await this.prisma.systemLog.create({
                data: {
                    userType: 'ADMIN',
                    action: logAction,
                    resource: 'company',
                    details: JSON.stringify({
                        companyId: companyId,
                        companyName: company.name,
                        externalCompanyId: company.externalCompanyId,
                        externalResponse: deleteResponse
                    })
                }
            });
            const message = isCompanyNotFound
                ? '公司在外部系统中不存在，本地绑定已清除'
                : company.externalCompanyId ? '公司同步删除成功' : '公司本地绑定已清除';
            console.log(`🎉 [同步删除] 操作成功: ${message}`);
            return {
                success: true,
                message: message
            };
        }
        catch (error) {
            // 7. 异常处理
            console.error(`💥 [同步删除] 发生异常:`, {
                companyId,
                error: error instanceof Error ? error.message : '未知错误',
                stack: error instanceof Error ? error.stack : undefined
            });
            // 判断是否为外部API网络错误
            const isNetworkError = error instanceof Error && (error.message.includes('fetch') ||
                error.message.includes('network') ||
                error.message.includes('ECONNREFUSED') ||
                error.message.includes('timeout'));
            const errorType = isNetworkError ? 'EXTERNAL_API_ERROR' : 'BACKEND_VALIDATION';
            // 记录错误日志
            try {
                await this.prisma.systemLog.create({
                    data: {
                        userType: 'ADMIN',
                        action: 'SYNC_DELETE_COMPANY_FAILED',
                        resource: 'company',
                        details: JSON.stringify({
                            companyId: companyId,
                            error: error instanceof Error ? error.message : '未知错误',
                            errorType: errorType,
                            stack: error instanceof Error ? error.stack : undefined
                        })
                    }
                });
            }
            catch (logError) {
                console.error('📝 [日志记录] 失败:', logError);
            }
            return {
                success: false,
                message: error instanceof Error ? error.message : '同步删除公司失败',
                errorType: errorType
            };
        }
    }
    /**
     * 批量同步公司状态
     */
    async batchSyncCompanies(companyIds) {
        const results = [];
        let successCount = 0;
        let failedCount = 0;
        for (const companyId of companyIds) {
            const result = await this.syncCreateCompany(companyId);
            results.push({
                companyId,
                success: result.success,
                message: result.message
            });
            if (result.success) {
                successCount++;
            }
            else {
                failedCount++;
            }
        }
        return {
            success: successCount,
            failed: failedCount,
            results
        };
    }
}
exports.ExternalCompanyService = ExternalCompanyService;
//# sourceMappingURL=external-company.service.js.map