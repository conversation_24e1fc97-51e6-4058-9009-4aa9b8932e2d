/**
 * 发票服务
 */
import { BaseService } from './base.service';
export interface InvoiceListQuery {
    page?: number;
    pageSize?: number;
    search?: string;
    status?: string;
    type?: string;
    merchantId?: number;
    companyId?: number;
}
export declare class InvoiceService extends BaseService {
    /**
     * 获取发票列表
     */
    getInvoiceList(query: InvoiceListQuery): Promise<{
        invoices: any[];
        total: number;
    }>;
    /**
     * 获取发票详情
     */
    getInvoiceDetail(id: number): Promise<any>;
    /**
     * 创建发票
     */
    createInvoice(data: any): Promise<any>;
    /**
     * 更新发票
     */
    updateInvoice(id: number, data: any): Promise<any>;
    /**
     * 删除发票
     */
    deleteInvoice(id: number): Promise<any>;
    /**
     * 更新发票状态
     */
    updateInvoiceStatus(id: number, status: string): Promise<any>;
}
//# sourceMappingURL=invoice.service.d.ts.map