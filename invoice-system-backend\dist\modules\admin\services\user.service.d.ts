/**
 * 用户服务
 */
import { BaseService } from './base.service';
import { PaginationQuery, UserStats } from '../../../shared/interfaces/types';
export declare class UserService extends BaseService {
    /**
     * 获取用户列表
     */
    getUserList(query: PaginationQuery & {
        search?: string;
        status?: string;
    }): Promise<{
        users: any[];
        total: number;
    }>;
    /**
     * 获取用户详情
     */
    getUserDetail(id: number): Promise<any>;
    /**
     * 创建用户
     */
    createUser(data: any): Promise<any>;
    /**
     * 更新用户
     */
    updateUser(id: number, data: any): Promise<any>;
    /**
     * 删除用户
     */
    deleteUser(id: number): Promise<void>;
    /**
     * 获取用户统计信息
     */
    getUserStats(): Promise<UserStats>;
}
//# sourceMappingURL=user.service.d.ts.map