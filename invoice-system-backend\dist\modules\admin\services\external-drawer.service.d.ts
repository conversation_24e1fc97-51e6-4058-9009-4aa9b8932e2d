/**
 * 开票员外部API集成服务
 */
import { PrismaClient } from '@prisma/client';
export declare class ExternalDrawerService {
    private prisma;
    private drawerApiService;
    constructor(prisma: PrismaClient);
    /**
     * 创建开票员到外部系统
     */
    createDrawer(drawerData: {
        drawer_name: string;
        id_number: string;
        phone_number: string;
        login_pwd: string;
    }): Promise<{
        success: boolean;
        drawer_code?: string;
        message: string;
        errorType?: 'FRONTEND_VALIDATION' | 'BACKEND_VALIDATION' | 'EXTERNAL_API_BUSINESS' | 'EXTERNAL_API_ERROR';
    }>;
    /**
     * 绑定开票员到公司并同步外部API
     */
    bindDrawerWithSync(staffId: number, companyId: number): Promise<{
        success: boolean;
        data?: any;
        message: string;
        errorType?: 'FRONTEND_VALIDATION' | 'BACKEND_VALIDATION' | 'EXTERNAL_API_BUSINESS' | 'EXTERNAL_API_ERROR';
    }>;
}
//# sourceMappingURL=external-drawer.service.d.ts.map