"use strict";
/**
 * 用户管理控制器
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserAdminController = void 0;
const base_admin_controller_1 = require("./base-admin.controller");
const response_1 = require("../../../shared/utils/response");
const systemLog_decorator_1 = require("../../../shared/decorators/systemLog.decorator");
class UserAdminController extends base_admin_controller_1.BaseAdminController {
    /**
     * 获取用户列表
     */
    async getUserList(req, res) {
        try {
            const { page, pageSize, search, status } = this.getPaginationParams(req);
            const result = await this.adminService.getUserList({
                page,
                pageSize,
                search,
                status
            });
            // 返回符合前端期望的数据格式
            res.json((0, response_1.successResponse)({
                users: result.users,
                pagination: {
                    page,
                    limit: pageSize,
                    total: result.total,
                    totalPages: Math.ceil(result.total / pageSize)
                }
            }, '获取用户列表成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 获取用户详情
     */
    async getUserDetail(req, res) {
        try {
            const { id } = req.params;
            const user = await this.adminService.getUserDetail(parseInt(id));
            res.json((0, response_1.successResponse)(user, '获取用户详情成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 创建用户
     */
    async createUser(req, res) {
        try {
            const userData = req.body;
            const user = await this.adminService.createUser(userData);
            res.json((0, response_1.successResponse)(user, '创建用户成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 更新用户
     */
    async updateUser(req, res) {
        try {
            const { id } = req.params;
            const updateData = req.body;
            const user = await this.adminService.updateUser(parseInt(id), updateData);
            res.json((0, response_1.successResponse)(user, '更新用户成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 删除用户
     */
    async deleteUser(req, res) {
        try {
            const { id } = req.params;
            await this.adminService.deleteUser(parseInt(id));
            res.json((0, response_1.successResponse)(null, '删除用户成功'));
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * 获取用户统计信息
     */
    async getUserStats(req, res) {
        try {
            const stats = await this.adminService.getUserStats();
            res.json((0, response_1.successResponse)(stats, '获取用户统计信息成功'));
        }
        catch (error) {
            throw error;
        }
    }
}
exports.UserAdminController = UserAdminController;
__decorate([
    (0, systemLog_decorator_1.LogView)('USER_LIST'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], UserAdminController.prototype, "getUserList", null);
__decorate([
    (0, systemLog_decorator_1.LogView)('USER_DETAIL'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], UserAdminController.prototype, "getUserDetail", null);
__decorate([
    (0, systemLog_decorator_1.LogView)('USER_CREATE'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], UserAdminController.prototype, "createUser", null);
__decorate([
    (0, systemLog_decorator_1.LogView)('USER_UPDATE'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], UserAdminController.prototype, "updateUser", null);
__decorate([
    (0, systemLog_decorator_1.LogView)('USER_DELETE'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], UserAdminController.prototype, "deleteUser", null);
__decorate([
    (0, systemLog_decorator_1.LogView)('USER_STATS'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], UserAdminController.prototype, "getUserStats", null);
//# sourceMappingURL=user-admin.controller.js.map