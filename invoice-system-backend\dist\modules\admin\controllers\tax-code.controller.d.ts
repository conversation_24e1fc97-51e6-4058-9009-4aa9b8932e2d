/**
 * 税收编码管理控制器
 */
import { Request, Response } from 'express';
import { BaseAdminController } from './base-admin.controller';
import { IAdminService } from '../interfaces/admin-service.interface';
import { PrismaClient } from '@prisma/client';
export declare class TaxCodeController extends BaseAdminController {
    private taxCodeService;
    constructor(adminService: IAdminService, prisma: PrismaClient);
    getTaxCodes(req: Request, res: Response): Promise<void>;
    getTaxCode(req: Request, res: Response): Promise<void>;
    createTaxCode(req: Request, res: Response): Promise<void>;
    updateTaxCode(req: Request, res: Response): Promise<void>;
    deleteTaxCode(req: Request, res: Response): Promise<void>;
    searchTaxCodes(req: Request, res: Response): Promise<void>;
    importTaxCodes(req: Request, res: Response): Promise<void>;
    exportTaxCodes(req: Request, res: Response): Promise<void>;
    getTaxCodeStats(req: Request, res: Response): Promise<void>;
    updateTaxCodeStatus(req: Request, res: Response): Promise<void>;
}
//# sourceMappingURL=tax-code.controller.d.ts.map