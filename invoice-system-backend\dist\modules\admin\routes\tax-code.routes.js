"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const client_1 = require("@prisma/client");
const tax_code_controller_1 = require("../controllers/tax-code.controller");
const admin_service_module_1 = require("../services/admin-service.module");
const multer_1 = __importDefault(require("multer"));
const router = (0, express_1.Router)();
const prisma = new client_1.PrismaClient();
const adminService = new admin_service_module_1.AdminServiceModule(prisma);
const taxCodeController = new tax_code_controller_1.TaxCodeController(adminService, prisma);
// 配置文件上传
const upload = (0, multer_1.default)({
    storage: multer_1.default.memoryStorage(),
    limits: { fileSize: 10 * 1024 * 1024 } // 10MB
});
// 税收编码路由
router.get('/', taxCodeController.getTaxCodes.bind(taxCodeController));
router.get('/search', taxCodeController.searchTaxCodes.bind(taxCodeController));
router.get('/stats', taxCodeController.getTaxCodeStats.bind(taxCodeController));
router.get('/export', taxCodeController.exportTaxCodes.bind(taxCodeController));
router.get('/:id', taxCodeController.getTaxCode.bind(taxCodeController));
router.post('/', taxCodeController.createTaxCode.bind(taxCodeController));
router.post('/import', upload.single('file'), taxCodeController.importTaxCodes.bind(taxCodeController));
router.put('/:id', taxCodeController.updateTaxCode.bind(taxCodeController));
router.patch('/:id/status', taxCodeController.updateTaxCodeStatus.bind(taxCodeController));
router.delete('/:id', taxCodeController.deleteTaxCode.bind(taxCodeController));
exports.default = router;
//# sourceMappingURL=tax-code.routes.js.map