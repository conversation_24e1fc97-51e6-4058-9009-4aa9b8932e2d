/**
 * 公司外部API集成服务
 */
import { PrismaClient } from '@prisma/client';
export declare class ExternalCompanyService {
    private prisma;
    private companyApiService;
    constructor(prisma: PrismaClient);
    /**
     * 同步创建公司到外部系统
     */
    syncCreateCompany(companyId: number): Promise<{
        success: boolean;
        externalCompanyId?: string;
        message: string;
    }>;
    /**
     * 从外部系统删除公司
     */
    syncDeleteCompany(companyId: number): Promise<{
        success: boolean;
        message: string;
        errorType?: 'FRONTEND_VALIDATION' | 'BACKEND_VALIDATION' | 'EXTERNAL_API_ERROR' | 'EXTERNAL_API_BUSINESS';
    }>;
    /**
     * 批量同步公司状态
     */
    batchSyncCompanies(companyIds: number[]): Promise<{
        success: number;
        failed: number;
        results: Array<{
            companyId: number;
            success: boolean;
            message: string;
        }>;
    }>;
}
//# sourceMappingURL=external-company.service.d.ts.map