"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvoiceTitleAdminController = void 0;
const client_1 = require("@prisma/client");
const base_admin_controller_1 = require("./base-admin.controller");
const systemLog_decorator_1 = require("../../../shared/decorators/systemLog.decorator");
const prisma = new client_1.PrismaClient();
class InvoiceTitleAdminController extends base_admin_controller_1.BaseAdminController {
    /**
     * 获取发票抬头列表
     */
    async getInvoiceTitles(req, res) {
        try {
            const { page = 1, limit = 10, search, type, userId, isDefault } = req.query;
            const pageNum = parseInt(page);
            const limitNum = parseInt(limit);
            const skip = (pageNum - 1) * limitNum;
            // 构建查询条件
            const where = {};
            if (search) {
                where.OR = [
                    { name: { contains: search } },
                    { taxNumber: { contains: search } },
                    { address: { contains: search } },
                    { phone: { contains: search } }
                ];
            }
            if (type) {
                where.type = type;
            }
            if (userId) {
                where.userId = parseInt(userId);
            }
            if (isDefault !== undefined) {
                where.isDefault = isDefault === 'true';
            }
            // 获取总数
            const total = await prisma.invoiceTitle.count({ where });
            // 获取数据
            const invoiceTitles = await prisma.invoiceTitle.findMany({
                where,
                skip,
                take: limitNum,
                include: {
                    user: {
                        select: {
                            id: true,
                            username: true,
                            email: true,
                            phone: true,
                            status: true
                        }
                    }
                },
                orderBy: { createdAt: 'desc' }
            });
            // 统计信息
            const stats = {
                total,
                companyCount: await prisma.invoiceTitle.count({ where: { ...where, type: 'COMPANY' } }),
                personalCount: await prisma.invoiceTitle.count({ where: { ...where, type: 'PERSONAL' } }),
                defaultCount: await prisma.invoiceTitle.count({ where: { ...where, isDefault: true } })
            };
            res.json({
                success: true,
                data: {
                    items: invoiceTitles,
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total,
                        pages: Math.ceil(total / limitNum)
                    },
                    stats
                }
            });
        }
        catch (error) {
            console.error('获取发票抬头列表失败:', error);
            res.status(500).json({
                success: false,
                message: '获取发票抬头列表失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
    /**
     * 获取发票抬头详情
     */
    async getInvoiceTitle(req, res) {
        try {
            const { id } = req.params;
            const invoiceTitle = await prisma.invoiceTitle.findUnique({
                where: { id: parseInt(id) },
                include: {
                    user: {
                        select: {
                            id: true,
                            username: true,
                            email: true,
                            phone: true,
                            status: true
                        }
                    }
                }
            });
            if (!invoiceTitle) {
                return res.status(404).json({
                    success: false,
                    message: '发票抬头不存在'
                });
            }
            res.json({
                success: true,
                data: invoiceTitle
            });
        }
        catch (error) {
            console.error('获取发票抬头详情失败:', error);
            res.status(500).json({
                success: false,
                message: '获取发票抬头详情失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
    /**
     * 创建发票抬头
     */
    async createInvoiceTitle(req, res) {
        try {
            const { userId, type, name, taxNumber, address, phone, bankName, bankAccount, isDefault } = req.body;
            // 验证必填字段
            if (!userId || !type || !name) {
                return res.status(400).json({
                    success: false,
                    message: '用户ID、类型和名称为必填字段'
                });
            }
            // 验证用户是否存在
            const user = await prisma.user.findUnique({
                where: { id: userId }
            });
            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: '用户不存在'
                });
            }
            // 如果设置为默认，先取消该用户的其他默认抬头
            if (isDefault) {
                await prisma.invoiceTitle.updateMany({
                    where: { userId, isDefault: true },
                    data: { isDefault: false }
                });
            }
            const invoiceTitle = await prisma.invoiceTitle.create({
                data: {
                    userId,
                    type,
                    name,
                    taxNumber,
                    address,
                    phone,
                    bankName,
                    bankAccount,
                    isDefault: isDefault || false
                },
                include: {
                    user: {
                        select: {
                            id: true,
                            username: true,
                            email: true,
                            phone: true,
                            status: true
                        }
                    }
                }
            });
            res.status(201).json({
                success: true,
                message: '发票抬头创建成功',
                data: invoiceTitle
            });
        }
        catch (error) {
            console.error('创建发票抬头失败:', error);
            res.status(500).json({
                success: false,
                message: '创建发票抬头失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
    /**
     * 更新发票抬头
     */
    async updateInvoiceTitle(req, res) {
        try {
            const { id } = req.params;
            const { type, name, taxNumber, address, phone, bankName, bankAccount, isDefault } = req.body;
            // 检查发票抬头是否存在
            const existingTitle = await prisma.invoiceTitle.findUnique({
                where: { id: parseInt(id) }
            });
            if (!existingTitle) {
                return res.status(404).json({
                    success: false,
                    message: '发票抬头不存在'
                });
            }
            // 如果设置为默认，先取消该用户的其他默认抬头
            if (isDefault) {
                await prisma.invoiceTitle.updateMany({
                    where: {
                        userId: existingTitle.userId,
                        isDefault: true,
                        id: { not: parseInt(id) }
                    },
                    data: { isDefault: false }
                });
            }
            const invoiceTitle = await prisma.invoiceTitle.update({
                where: { id: parseInt(id) },
                data: {
                    type,
                    name,
                    taxNumber,
                    address,
                    phone,
                    bankName,
                    bankAccount,
                    isDefault
                },
                include: {
                    user: {
                        select: {
                            id: true,
                            username: true,
                            email: true,
                            phone: true,
                            status: true
                        }
                    }
                }
            });
            res.json({
                success: true,
                message: '发票抬头更新成功',
                data: invoiceTitle
            });
        }
        catch (error) {
            console.error('更新发票抬头失败:', error);
            res.status(500).json({
                success: false,
                message: '更新发票抬头失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
    /**
     * 删除发票抬头
     */
    async deleteInvoiceTitle(req, res) {
        try {
            const { id } = req.params;
            // 检查发票抬头是否存在
            const existingTitle = await prisma.invoiceTitle.findUnique({
                where: { id: parseInt(id) }
            });
            if (!existingTitle) {
                return res.status(404).json({
                    success: false,
                    message: '发票抬头不存在'
                });
            }
            await prisma.invoiceTitle.delete({
                where: { id: parseInt(id) }
            });
            res.json({
                success: true,
                message: '发票抬头删除成功'
            });
        }
        catch (error) {
            console.error('删除发票抬头失败:', error);
            res.status(500).json({
                success: false,
                message: '删除发票抬头失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
    /**
     * 批量删除发票抬头
     */
    async batchDeleteInvoiceTitles(req, res) {
        try {
            const { ids } = req.body;
            if (!Array.isArray(ids) || ids.length === 0) {
                return res.status(400).json({
                    success: false,
                    message: '请提供要删除的发票抬头ID列表'
                });
            }
            const result = await prisma.invoiceTitle.deleteMany({
                where: {
                    id: { in: ids.map(id => parseInt(id)) }
                }
            });
            res.json({
                success: true,
                message: `成功删除 ${result.count} 个发票抬头`,
                data: { deletedCount: result.count }
            });
        }
        catch (error) {
            console.error('批量删除发票抬头失败:', error);
            res.status(500).json({
                success: false,
                message: '批量删除发票抬头失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
    /**
     * 获取发票抬头统计信息
     */
    async getInvoiceTitleStats(req, res) {
        try {
            const total = await prisma.invoiceTitle.count();
            const companyCount = await prisma.invoiceTitle.count({ where: { type: 'COMPANY' } });
            const personalCount = await prisma.invoiceTitle.count({ where: { type: 'PERSONAL' } });
            const defaultCount = await prisma.invoiceTitle.count({ where: { isDefault: true } });
            // 按用户统计
            const userStats = await prisma.invoiceTitle.groupBy({
                by: ['userId'],
                _count: { id: true },
                orderBy: { _count: { id: 'desc' } },
                take: 10
            });
            // 获取用户信息
            const userIds = userStats.map(stat => stat.userId);
            const users = await prisma.user.findMany({
                where: { id: { in: userIds } },
                select: { id: true, username: true }
            });
            const userStatsWithNames = userStats.map(stat => ({
                userId: stat.userId,
                username: users.find(u => u.id === stat.userId)?.username || '未知用户',
                count: stat._count.id
            }));
            // 最近创建的抬头
            const recentTitles = await prisma.invoiceTitle.findMany({
                take: 5,
                orderBy: { createdAt: 'desc' },
                include: {
                    user: {
                        select: { username: true }
                    }
                }
            });
            res.json({
                success: true,
                data: {
                    overview: {
                        total,
                        companyCount,
                        personalCount,
                        defaultCount
                    },
                    userStats: userStatsWithNames,
                    recentTitles
                }
            });
        }
        catch (error) {
            console.error('获取发票抬头统计信息失败:', error);
            res.status(500).json({
                success: false,
                message: '获取发票抬头统计信息失败',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    }
}
exports.InvoiceTitleAdminController = InvoiceTitleAdminController;
__decorate([
    (0, systemLog_decorator_1.SystemLog)('获取发票抬头列表', 'invoice_title'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], InvoiceTitleAdminController.prototype, "getInvoiceTitles", null);
__decorate([
    (0, systemLog_decorator_1.SystemLog)('获取发票抬头详情', 'invoice_title'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], InvoiceTitleAdminController.prototype, "getInvoiceTitle", null);
__decorate([
    (0, systemLog_decorator_1.SystemLog)('创建发票抬头', 'invoice_title'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], InvoiceTitleAdminController.prototype, "createInvoiceTitle", null);
__decorate([
    (0, systemLog_decorator_1.SystemLog)('更新发票抬头', 'invoice_title'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], InvoiceTitleAdminController.prototype, "updateInvoiceTitle", null);
__decorate([
    (0, systemLog_decorator_1.SystemLog)('删除发票抬头', 'invoice_title'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], InvoiceTitleAdminController.prototype, "deleteInvoiceTitle", null);
__decorate([
    (0, systemLog_decorator_1.SystemLog)('批量删除发票抬头', 'invoice_title'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], InvoiceTitleAdminController.prototype, "batchDeleteInvoiceTitles", null);
__decorate([
    (0, systemLog_decorator_1.SystemLog)('获取发票抬头统计信息', 'invoice_title'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], InvoiceTitleAdminController.prototype, "getInvoiceTitleStats", null);
//# sourceMappingURL=invoice-title-admin.controller.js.map