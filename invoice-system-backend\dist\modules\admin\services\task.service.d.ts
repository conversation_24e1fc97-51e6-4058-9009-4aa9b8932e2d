/**
 * 任务服务
 */
import { BaseService } from './base.service';
import { PaginationQuery } from '../../../shared/interfaces/types';
export declare class TaskService extends BaseService {
    /**
     * 获取任务列表
     */
    getTaskList(query: PaginationQuery & {
        search?: string;
        status?: string;
        type?: string;
    }): Promise<{
        tasks: any[];
        total: number;
    }>;
}
//# sourceMappingURL=task.service.d.ts.map