/**
 * 系统日志服务
 */
import { BaseService } from './base.service';
import { SystemLogListQuery, SystemUserType } from '../../../shared/interfaces/types';
export declare class SystemLogService extends BaseService {
    /**
     * 获取系统日志列表
     */
    getSystemLogList(query: SystemLogListQuery): Promise<{
        logs: any[];
        total: number;
    }>;
    /**
     * 创建系统日志
     */
    createSystemLog(data: {
        adminId?: number;
        merchantId?: number;
        userId?: number;
        userType: SystemUserType;
        action: string;
        resource: string;
        details?: any;
        ip?: string;
        userAgent?: string;
        level?: string;
        context?: any;
    }): Promise<any>;
    /**
     * 获取系统日志统计信息
     */
    getSystemLogStats(): Promise<{
        totalLogs: number;
        todayLogs: number;
        errorLogs: number;
        warnLogs: number;
        infoLogs: number;
        debugLogs: number;
        recentErrorLogs: any[];
    }>;
    /**
     * 获取操作统计
     */
    getActionStats(days?: number): Promise<{
        actionCounts: {
            action: string;
            count: number;
        }[];
        resourceCounts: {
            resource: string;
            count: number;
        }[];
        userTypeCounts: {
            userType: string;
            count: number;
        }[];
    }>;
    /**
     * 清理过期日志
     */
    cleanupOldLogs(days?: number): Promise<{
        deletedCount: number;
    }>;
    /**
     * 获取日志详情
     */
    getSystemLogDetail(id: number): Promise<any>;
}
//# sourceMappingURL=system-log.service.d.ts.map